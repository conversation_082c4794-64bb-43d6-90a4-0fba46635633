import request from '@/utils/request'
import type {
  TeachingGroup,
  Teacher,
  GetTeachingGroupsParams,
  CreateTeachingGroupRequest,
  UpdateTeachingGroupRequest,
  ApiResponse,
  PageResponse
} from './types'

/**
 * 获取教学组列表
 */
export function getTeachingGroupsApi(params: GetTeachingGroupsParams): Promise<ApiResponse<PageResponse<TeachingGroup>>> {
  return request({
    url: '/management/teaching-groups',
    method: 'get',
    params
  })
}

/**
 * 创建教学组
 */
export function createTeachingGroupApi(data: CreateTeachingGroupRequest): Promise<ApiResponse<void>> {
  return request({
    url: '/management/teaching-groups',
    method: 'post',
    data
  })
}

/**
 * 更新教学组
 */
export function updateTeachingGroupApi(id: string, data: UpdateTeachingGroupRequest): Promise<ApiResponse<void>> {
  return request({
    url: `/management/teaching-groups/${id}`,
    method: 'put',
    data
  })
}

/**
 * 删除教学组
 */
export function deleteTeachingGroup<PERSON><PERSON>(id: string): Promise<ApiResponse<void>> {
  return request({
    url: `/management/teaching-groups/${id}`,
    method: 'delete'
  })
}

/**
 * 更换组长
 */
export function changeGroupLeaderApi(groupId: string, newLeaderId: string): Promise<ApiResponse<void>> {
  return request({
    url: `/management/teaching-groups/${groupId}/leader`,
    method: 'put',
    data: { leaderId: newLeaderId }
  })
}

/**
 * 添加老师到教学组
 */
export function addTeacherToGroupApi(groupId: string, teacherId: string): Promise<ApiResponse<void>> {
  return request({
    url: `/management/teaching-groups/${groupId}/members`,
    method: 'post',
    data: { teacherId }
  })
}

/**
 * 从教学组移除老师
 */
export function removeTeacherFromGroupApi(groupId: string, teacherId: string): Promise<ApiResponse<void>> {
  return request({
    url: `/management/teaching-groups/${groupId}/members/${teacherId}`,
    method: 'delete'
  })
}

/**
 * 获取教学组成员
 */
export function getGroupMembersApi(groupId: string): Promise<ApiResponse<Teacher[]>> {
  return request({
    url: `/management/teaching-groups/${groupId}/members`,
    method: 'get'
  })
}

// 导出所有API方法
export const teachingGroupApi = {
  getGroups: getTeachingGroupsApi,
  createGroup: createTeachingGroupApi,
  updateGroup: updateTeachingGroupApi,
  deleteGroup: deleteTeachingGroupApi,
  changeGroupLeader: changeGroupLeaderApi,
  addTeacherToGroup: addTeacherToGroupApi,
  removeTeacherFromGroup: removeTeacherFromGroupApi,
  getGroupMembers: getGroupMembersApi
}

export default teachingGroupApi