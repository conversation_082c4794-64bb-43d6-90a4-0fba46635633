import request from '@/utils/request'
import type {
  Teacher,
  TeacherPermission,
  GetTeachersParams,
  CreateTeacherRequest,
  UpdateTeacherRequest,
  ApiResponse,
  PageResponse
} from './types'

/**
 * 获取老师列表
 */
export function getTeachersApi(params: GetTeachersParams): Promise<ApiResponse<PageResponse<Teacher>>> {
  return request({
    url: '/management/teachers',
    method: 'get',
    params
  })
}

/**
 * 创建老师
 */
export function createTeacherApi(data: CreateTeacherRequest): Promise<ApiResponse<void>> {
  return request({
    url: '/management/teachers',
    method: 'post',
    data
  })
}

/**
 * 更新老师信息
 */
export function updateTeacherApi(id: string, data: UpdateTeacherRequest): Promise<ApiResponse<void>> {
  return request({
    url: `/management/teachers/${id}`,
    method: 'put',
    data
  })
}

/**
 * 删除老师
 */
export function deleteTeacher<PERSON>pi(id: string): Promise<ApiResponse<void>> {
  return request({
    url: `/management/teachers/${id}`,
    method: 'delete'
  })
}

/**
 * 分配老师到教学组
 */
export function assignTeacherToGroupApi(teacherId: string, groupId: string): Promise<ApiResponse<void>> {
  return request({
    url: `/management/teachers/${teacherId}/group`,
    method: 'put',
    data: { groupId }
  })
}

/**
 * 获取老师权限
 */
export function getTeacherPermissionsApi(teacherId: string): Promise<ApiResponse<TeacherPermission>> {
  return request({
    url: `/management/teachers/${teacherId}/permissions`,
    method: 'get'
  })
}

/**
 * 批量分配教学组
 */
export function batchAssignGroupApi(teacherIds: string[], groupId: string): Promise<ApiResponse<void>> {
  return request({
    url: '/management/teachers/batch/assign-group',
    method: 'post',
    data: { teacherIds, groupId }
  })
}

// 导出所有API方法
export const teacherManagementApi = {
  getTeachers: getTeachersApi,
  createTeacher: createTeacherApi,
  updateTeacher: updateTeacherApi,
  deleteTeacher: deleteTeacherApi,
  assignTeacherToGroup: assignTeacherToGroupApi,
  getTeacherPermissions: getTeacherPermissionsApi,
  batchAssignGroup: batchAssignGroupApi
}

export default teacherManagementApi