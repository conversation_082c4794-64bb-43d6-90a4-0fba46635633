// API响应基础接口
export interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
}

// 分页响应
export interface PageResponse<T> {
  total: number
  rows: T[]
  pageNum: number
  pageSize: number
}

// 教学组数据模型
export interface TeachingGroup {
  id: string
  name: string
  subject?: string
  leaderId: string
  leaderName: string
  description?: string
  teacherCount: number
  status: 'active' | 'inactive'
  createdAt: string
  updatedAt: string
}

// 老师数据模型
export interface Teacher {
  id: string
  name: string
  phone: string
  email?: string
  avatar?: string
  subjects?: string[]
  groupId?: string
  groupName?: string
  role: 'admin' | 'group_leader' | 'teacher'
  status: 'active' | 'inactive'
  joinedAt?: string
  createdAt: string
}

// 权限数据模型
export interface TeacherPermission {
  userId: string
  role: 'admin' | 'group_leader' | 'teacher'
  canManageGroups: boolean
  canManageTeachers: boolean
  canViewAllSchedules: boolean
  canScheduleForOthers: boolean
  managedGroupIds: string[]
}

// API请求参数
export interface GetTeachingGroupsParams {
  keyword?: string
  status?: 'active' | 'inactive'
  pageNum?: number
  pageSize?: number
}

export interface CreateTeachingGroupRequest {
  name: string
  subject?: string
  leaderId: string
  description?: string
}

export interface UpdateTeachingGroupRequest {
  name?: string
  subject?: string
  leaderId?: string
  description?: string
}

export interface GetTeachersParams {
  keyword?: string
  groupId?: string
  role?: string
  status?: 'active' | 'inactive'
  pageNum?: number
  pageSize?: number
}

export interface CreateTeacherRequest {
  name: string
  phone: string
  email?: string
  subjects?: string[]
  role: 'teacher' | 'group_leader'
  groupId?: string
}

export interface UpdateTeacherRequest {
  name?: string
  phone?: string
  email?: string
  subjects?: string[]
  role?: 'teacher' | 'group_leader'
  groupId?: string
}

// 教学组管理API
export interface TeachingGroupAPI {
  // 获取教学组列表
  getGroups(params: GetTeachingGroupsParams): Promise<ApiResponse<PageResponse<TeachingGroup>>>
  
  // 创建教学组
  createGroup(data: CreateTeachingGroupRequest): Promise<ApiResponse<void>>
  
  // 更新教学组
  updateGroup(id: string, data: UpdateTeachingGroupRequest): Promise<ApiResponse<void>>
  
  // 删除教学组
  deleteGroup(id: string): Promise<ApiResponse<void>>
  
  // 更换组长
  changeGroupLeader(groupId: string, newLeaderId: string): Promise<ApiResponse<void>>
  
  // 添加老师到教学组
  addTeacherToGroup(groupId: string, teacherId: string): Promise<ApiResponse<void>>
  
  // 从教学组移除老师
  removeTeacherFromGroup(groupId: string, teacherId: string): Promise<ApiResponse<void>>
  
  // 获取教学组成员
  getGroupMembers(groupId: string): Promise<ApiResponse<Teacher[]>>
}

// 老师管理API
export interface TeacherManagementAPI {
  // 获取老师列表
  getTeachers(params: GetTeachersParams): Promise<ApiResponse<PageResponse<Teacher>>>
  
  // 创建老师
  createTeacher(data: CreateTeacherRequest): Promise<ApiResponse<void>>
  
  // 更新老师信息
  updateTeacher(id: string, data: UpdateTeacherRequest): Promise<ApiResponse<void>>
  
  // 删除老师
  deleteTeacher(id: string): Promise<ApiResponse<void>>
  
  // 分配老师到教学组
  assignTeacherToGroup(teacherId: string, groupId: string): Promise<ApiResponse<void>>
  
  // 获取老师权限
  getTeacherPermissions(teacherId: string): Promise<ApiResponse<TeacherPermission>>
}