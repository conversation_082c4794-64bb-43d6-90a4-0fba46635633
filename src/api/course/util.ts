import { ref } from "vue";
import { ElLoading, ElMessage } from 'element-plus'

const currentAudio = ref<HTMLAudioElement | null>(null);

export function playAudioUtil(url?: string) {
    // 停止当前音频currentStepInfo?.wordInfo?.audioUkUrl
    if (currentAudio.value) {
        currentAudio.value.pause();
        currentAudio.value.currentTime = 0;
        currentAudio.value = null;
    }

    // 播放新音频
    if (url) {
        const audio = new Audio(url);
        audio.play().catch(err => {
            console.error("音频播放失败：", err);
        });
        currentAudio.value = audio;
    }
}

export function getRandomStarStyle() {
    const x = Math.random() * 100
    const y = Math.random() * 100
    const scale = 0.5 + Math.random() * 0.5
    const delay = Math.random() * 0.5
    return {
        left: `${x}%`,
        top: `${y}%`,
        transform: `scale(${scale})`,
        animationDelay: `${delay}s`
    }
}

export function getOptionLabel(index: number) {
    return String.fromCharCode(65 + index)
}
export function getOptionIndex(label: string): number {
    return label.charCodeAt(0) - 65;
}

export function loading(text: string = '处理中...'): any {
    return ElLoading.service({
        lock: true,
        text: text,
        background: 'rgba(0, 0, 0, 0.7)'
    })
}
