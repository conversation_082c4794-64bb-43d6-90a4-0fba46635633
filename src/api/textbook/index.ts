export declare interface Textbook{
    id: string;
    name: string;
    description: string;
    typeName: string;
    type:string;
    cover:string;
    wordList: string;
    // tags: string[];
    statWordCnt: Number;
    statUnitCnt: Number;
    grade:number;
    gradeName:string;
    publisher:string;
    required:string;
    semester:number;
    semesterName:string;
}

export declare interface TextbookItem{
    id: Number;
    name: string;
}

export declare interface BookTree{
  nodeId:string;
  nodeType:number;
  wordId:string;
  nodeName:string;
  wordNum:number;
  checked:boolean;
  textbookType:string;
  parentNodeId:string;
  textbookId:string;
  childNodeList?:BookTree[];
  learnStatus:boolean;
  mistakes:string;
}

export declare interface TextbookTreeParam{
  nodeId?:string;
  nodeType:number;
  searchType?:string;
  studentId?:string;
  types?:string[];
  tags?:string[];
}

export declare interface LastWordInfo{
  textbookId?:string;
  unitItemId?:string;
  wordItemId?:string;
  word?:string;
  textbookTreeVo?:BookTree;
}

import request from '@/utils/request';

// 添加教材
export function addOrUpdateTextbook(data: any) {
  return request({
    url: '/word/textbook/addOrUpdate',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  });
}

// 添加教材
export function removeTextbook(data: any) {
  return request({
    url: '/word/textbook/remove',
    method: 'post',
    data: data
  });
}


// 获取教材列表
export function getTextbookList(params?: { pageNum: number; pageSize: number }) {
  return request({
    url: '/word/textbook/page',
    method: 'get',
    params: params
  });
}

// 获取教材列表
export async function getTextbookTree(params : TextbookTreeParam) {
  return request({
    url: '/word/textbook/tree',
    method: 'get',
    params: params
  });
}

// 获取教材列表
export async function getLastTextbookId(studentId: string,textbookId: string) {
  return request({
    url: `/student/progress/word/${studentId}`,
    method: 'get',
    params: {
      textbookId:textbookId
    }
  });
}

