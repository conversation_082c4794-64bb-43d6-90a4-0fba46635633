<template>
  <div class="curriculum-page">
    <!-- 页面头部 -->
    <!-- <div class="page-header">
      <div class="header-left">
        <h1 class="page-title">
          <el-icon class="title-icon"><Calendar /></el-icon>
          课表管理
        </h1>
        <div class="header-meta">
          <span class="current-time">{{ currentTime }}</span>
          <el-divider direction="vertical" />
          <span class="user-info">{{ userRole }} - {{ userName }}</span>
        </div>
      </div>
    </div> -->

    <!-- 主内容区 -->
    <div class="page-content">
      <!-- 左侧课表区域 -->
      <div class="schedule-section">
        <el-card class="schedule-card" shadow="never">
          <!-- 课表工具栏 -->
          <template #header>
            <div class="schedule-toolbar">
              <div class="view-controls">
                <el-radio-group v-model="currentView" @change="handleViewChange">
                  <el-radio-button label="week">
                    <el-icon><Grid /></el-icon>
                    <span>周视图</span>
                  </el-radio-button>
                  <el-radio-button label="month">
                    <el-icon><Calendar /></el-icon>
                    <span>月视图</span>
                  </el-radio-button>
                </el-radio-group>
              </div>

              <div class="schedule-info">
                <el-button
                :icon="Plus"
                @click="showScheduleDialog = true"
                v-if="canCreateSchedule"
                circle
                >
                </el-button>
                <el-button :icon="Refresh" @click="refreshData" :loading="loading" circle />
                <el-tag type="info" size="small"> 共 {{ totalCourses }} 节课 </el-tag>
                <el-tag type="warning" size="small" v-if="todayCourses > 0">
                  今日 {{ todayCourses }} 节
                </el-tag>
              </div>
            </div>
          </template>

          <!-- 课表内容 -->
          <div class="schedule-content" v-loading="loading">
            <!-- 周视图 -->
            <WeekView
              v-if="currentView === 'week'"
              :courses="currentSchedules"
              :show-student-name="showStudentName"
              :show-teacher-name="showTeacherName"
              @week-change="handleDateChange"
              @slot-click="handleSlotClick"
              @course-action="handleCourseAction"
            />

            <!-- 月视图 -->
            <MonthView
              v-else-if="currentView === 'month'"
              :courses="currentSchedules"
              :show-student-name="showStudentName"
              :show-teacher-name="showTeacherName"
              @month-change="handleDateChange"
              @date-click="handleDateClick"
              @course-action="handleCourseAction"
            />

            <!-- 空状态 -->
            <div v-if="!loading && currentSchedules.length === 0" class="empty-schedule">
              <el-empty description="暂无课程安排" :image-size="120">
                <template #image>
                  <el-icon class="empty-icon"><Calendar /></el-icon>
                </template>
                <el-button
                  type="primary"
                  @click="showScheduleDialog = true"
                  v-if="canCreateSchedule"
                >
                  立即排课
                </el-button>
              </el-empty>
            </div>
          </div>
        </el-card>
      </div>

      <!-- 右侧复习课区域 -->
      <div class="review-section">
        <ReviewPanel
          :teacher-id="currentUser.role === 'teacher' ? currentUser.id : ''"
          :student-id="currentUser.role === 'student' ? currentUser.id : ''"
          @review-start="handleReviewStart"
          @review-complete="handleReviewComplete"
          @review-view="handleReviewView"
        />
      </div>
    </div>

    <!-- 排课弹窗 - 只有在需要时才创建 -->
    <ScheduleDialog
      v-if="showScheduleDialog"
      v-model="showScheduleDialog"
      @success="handleScheduleSuccess"
    />

    <!-- 快捷操作悬浮按钮 -->
    <div class="floating-actions" v-if="isMobile">
      <el-button
        type="primary"
        :icon="Plus"
        @click="showScheduleDialog = true"
        circle
        size="large"
        class="floating-btn"
        v-if="canCreateSchedule"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch } from "vue";
import { useRouter } from "vue-router";
import { ElMessage } from "element-plus";
import { Calendar, Grid, Plus, Refresh } from "@element-plus/icons-vue";
import { useCurriculumStore } from "@/stores/curriculum";
import useUserStore from "@/store/modules/user";
import WeekView from "./components/WeekView.vue";
import ReviewPanel from "./components/ReviewPanel.vue";
import ScheduleDialog from "./components/ScheduleDialog.vue";

const router = useRouter();
const curriculumStore = useCurriculumStore();
const userStore = useUserStore();

// 响应式数据
const loading = ref(false);
const showScheduleDialog = ref(false);
const currentTimeInterval = ref(null);
const currentTimeText = ref("");

// 从用户store获取当前用户信息
const currentUser = computed(() => ({
  id: userStore.id,
  name: userStore.name,
  role: "teacher", // 暂时固定为teacher，实际应该从userStore.roles判断
}));

// 计算属性
const currentView = computed({
  get: () => curriculumStore.currentView,
  set: (value) => curriculumStore.setCurrentView(value),
});

const currentSchedules = computed(() => {
  if (currentView.value === "week") {
    return curriculumStore.currentWeekSchedules;
  } else {
    return curriculumStore.currentMonthSchedules;
  }
});

const totalCourses = computed(() => {
  console.log("=== totalCourses computed DEBUG ===");
  console.log(
    "curriculumStore.schedules:",
    curriculumStore.schedules,
    "type:",
    typeof curriculumStore.schedules,
    "isArray:",
    Array.isArray(curriculumStore.schedules)
  );

  if (!Array.isArray(curriculumStore.schedules)) {
    console.error(
      "curriculumStore.schedules is not an array:",
      curriculumStore.schedules
    );
    return 0;
  }

  return curriculumStore.schedules.length;
});

const todayCourses = computed(() => {
  console.log("=== todayCourses computed DEBUG ===");
  console.log(
    "curriculumStore.schedules:",
    curriculumStore.schedules,
    "type:",
    typeof curriculumStore.schedules,
    "isArray:",
    Array.isArray(curriculumStore.schedules)
  );

  if (!Array.isArray(curriculumStore.schedules)) {
    console.error(
      "curriculumStore.schedules is not an array:",
      curriculumStore.schedules
    );
    return 0;
  }

  const today = new Date().toDateString();
  return curriculumStore.schedules.filter((course) => {
    return new Date(course.startTime).toDateString() === today;
  }).length;
});

const currentTime = computed(() => currentTimeText.value);

const userName = computed(() => currentUser.value.name);

const userRole = computed(() => {
  const roleMap = {
    teacher: "老师",
    student: "学生",
    admin: "管理员",
  };
  return roleMap[currentUser.value.role] || "用户";
});

// 权限控制
const canCreateSchedule = computed(() => {
  return ["teacher", "admin"].includes(currentUser.value.role);
});

const showStudentName = computed(() => {
  return currentUser.value.role !== "student";
});

const showTeacherName = computed(() => {
  return currentUser.value.role !== "teacher";
});

const isMobile = computed(() => {
  return window.innerWidth <= 768;
});

// 方法
const updateCurrentTime = () => {
  currentTimeText.value = new Date().toLocaleString("zh-CN", {
    weekday: "long",
    year: "numeric",
    month: "long",
    day: "numeric",
    hour: "2-digit",
    minute: "2-digit",
  });
};

const startTimeUpdate = () => {
  updateCurrentTime();
  currentTimeInterval.value = setInterval(updateCurrentTime, 60000); // 每分钟更新
};

const stopTimeUpdate = () => {
  if (currentTimeInterval.value) {
    clearInterval(currentTimeInterval.value);
    currentTimeInterval.value = null;
  }
};

const fetchScheduleData = async () => {
  loading.value = true;
  try {
    const params = {
      viewType: currentView.value,
      startDate: formatDate(getViewStartDate()),
      endDate: formatDate(getViewEndDate()),
      type: 'all',
    };

    // 根据用户角色添加筛选条件
    if (currentUser.value.role === "teacher") {
      params.teacherId = currentUser.value.id;
    } else if (currentUser.value.role === "student") {
      params.studentId = currentUser.value.id;
    }

    // 修复后的日志
    console.log("🔧 [修复] fetchScheduleData 调用参数:", params);
    console.log("🔧 [修复] 现在包含 courseType=all 参数，将获取所有类型的课程");

    await curriculumStore.fetchSchedules(params);
  } finally {
    loading.value = false;
  }
};

const getViewStartDate = () => {
  if (currentView.value === "week") {
    return getStartOfWeek(curriculumStore.selectedDate);
  } else {
    return getStartOfMonth(curriculumStore.selectedDate);
  }
};

const getViewEndDate = () => {
  if (currentView.value === "week") {
    return getEndOfWeek(curriculumStore.selectedDate);
  } else {
    return getEndOfMonth(curriculumStore.selectedDate);
  }
};

const getStartOfWeek = (date) => {
  const start = new Date(date);
  const day = start.getDay();
  const diff = start.getDate() - day + (day === 0 ? -6 : 1);
  start.setDate(diff);
  start.setHours(0, 0, 0, 0);
  return start;
};

const getEndOfWeek = (date) => {
  const end = new Date(getStartOfWeek(date));
  end.setDate(end.getDate() + 6);
  end.setHours(23, 59, 59, 999);
  return end;
};

const getStartOfMonth = (date) => {
  const start = new Date(date.getFullYear(), date.getMonth(), 1);
  start.setHours(0, 0, 0, 0);
  return start;
};

const getEndOfMonth = (date) => {
  const end = new Date(date.getFullYear(), date.getMonth() + 1, 0);
  end.setHours(23, 59, 59, 999);
  return end;
};

const formatDate = (date) => {
  return date.toISOString().split("T")[0];
};

const refreshData = async () => {
  await fetchScheduleData();
  ElMessage.success("数据已刷新");
};

// 事件处理
const handleViewChange = (view) => {
  fetchScheduleData();
};

const handleDateChange = (date) => {
  fetchScheduleData();
};

const handleSlotClick = (slotData) => {
//   if (canCreateSchedule.value) {
//     // 可以在这里预填充排课表单的时间信息
//     showScheduleDialog.value = true;
//   }
};

const handleDateClick = (date) => {
  // 切换到周视图并显示选中的日期
  curriculumStore.setSelectedDate(new Date(date));
  curriculumStore.setCurrentView("week");
  fetchScheduleData();
};

const handleCourseAction = (course) => {
  // 课程操作后刷新数据
  fetchScheduleData();
};

const handleScheduleSuccess = () => {
  fetchScheduleData();
};

const handleReviewStart = (review) => {
  console.log("开始复习:", review);
};

const handleReviewComplete = (review) => {
  console.log("完成复习:", review);
};

const handleReviewView = (review) => {
  console.log("查看复习详情:", review);
};

// 监听选中日期变化
watch(
  () => curriculumStore.selectedDate,
  () => {
    fetchScheduleData();
  }
);

// 生命周期
onMounted(async () => {
  // 确保用户信息已加载
  if (!userStore.id) {
    try {
      await userStore.getInfo();
      console.log("用户信息加载完成:", userStore.id, userStore.name);
    } catch (error) {
      console.error("获取用户信息失败:", error);
      ElMessage.error("获取用户信息失败");
    }
  }

  startTimeUpdate();
  fetchScheduleData();
});

onUnmounted(() => {
  stopTimeUpdate();
});

// 导入月视图组件
import MonthView from "./components/MonthView.vue";
</script>

<style lang="scss" scoped>
.curriculum-page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f7fa;
}

.page-header {
  background: #fff;
  padding: 16px 24px;
  border-bottom: 1px solid #e5e7eb;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.header-left {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.page-title {
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;

  .title-icon {
    color: #3b82f6;
    font-size: 28px;
  }
}

.header-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #6b7280;
  font-size: 14px;

  .current-time {
    font-weight: 500;
  }

  .user-info {
    color: #3b82f6;
    font-weight: 500;
  }
}

.header-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.page-content {
  flex: 1;
  display: flex;
  gap: 20px;
  padding: 20px 24px;
  overflow: hidden;
}

.schedule-section {
  flex: 1;
  min-width: 0;
}

.schedule-card {
  height: 100%;

  :deep(.el-card__body) {
    padding: 0;
    height: calc(100% - 60px);
  }
}

.schedule-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .view-controls {
    .el-radio-button {
      :deep(.el-radio-button__inner) {
        display: flex;
        align-items: center;
        gap: 4px;
      }
    }
  }

  .schedule-info {
    display: flex;
    gap: 8px;
  }
}

.schedule-content {
  height: 100%;
  overflow: hidden;
}

.empty-schedule {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;

  .empty-icon {
    font-size: 80px;
    color: #d1d5db;
  }
}

.review-section {
  width: 320px;
  flex-shrink: 0;
}

.floating-actions {
  position: fixed;
  bottom: 24px;
  right: 24px;
  z-index: 1000;

  .floating-btn {
    width: 56px;
    height: 56px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);

    &:hover {
      transform: scale(1.1);
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .review-section {
    width: 280px;
  }
}

@media (max-width: 768px) {
  .page-header {
    padding: 12px 16px;
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }

  .header-left {
    width: 100%;
  }

  .header-actions {
    width: 100%;
    justify-content: flex-end;
  }

  .page-content {
    flex-direction: column;
    padding: 16px;
    gap: 16px;
  }

  .review-section {
    width: 100%;
    max-height: 400px;
  }

  .schedule-toolbar {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }
}

@media (max-width: 480px) {
  .page-title {
    font-size: 20px;

    .title-icon {
      font-size: 24px;
    }
  }

  .header-meta {
    font-size: 12px;
  }

  .view-controls {
    .el-radio-group {
      width: 100%;

      .el-radio-button {
        flex: 1;
      }
    }
  }
}
</style>
