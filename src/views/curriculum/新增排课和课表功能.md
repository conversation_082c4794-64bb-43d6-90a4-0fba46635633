## 新增排课和课表功能
### 课程表页面

1. 显示老师的课表，分两块数据，课程表和复习课。主内容区位课程表，右侧显示复习课。
2. 课程表为主内容，可以切换周视图和月视图，每一节课占用的时间需要根据课程时长动态调整。
3. 复习课有两个标签页，今日（默认）和未来，都需要在标签名称后显示数量。
4. 课程表一次课程显示的内容有：
    - 学生姓名（学生本人看的时候不显示）
    - 上课时间
    - 下课时间
    - 课程状态（待开始、进行中、已完成、停课）
    - 老师姓名（老师看本人课表是不显示）
5. 课程表一次课程课操作的按钮有：
    - 开始上课
        - 点击打开把课程ID调用接口，返回成功后更新课程时间、状态，并在浏览器新标签页打开上课页面。
    - 停课
        - 状态不是已完成和停课时，可以操作
        - 点击停课按钮，弹窗输入停课原因（老师停课、学生停课）和说明后，点击确认按钮调用接口，成功后更新课程状态。
    - 结束上课
        - 状态是进行中时，可以操作
        - 点击结束上课按钮，确认框，点击确认按钮调用接口，成功后更新课程状态。
6. 复习课每一个标签也是一个列表
    - 每个列表项显示学生姓名、复习类型、单词数
    - 每个列表项操作按钮有：
        - 去复习
            - 点击打开复习页面，传入复习课ID。
        - 完成复习
            - 状态是进行中时，可以操作
            - 点击完成复习按钮，确认框，点击确认按钮调用接口，成功后更新复习状态。
7. 在老师管理页面上，会有查看课表的按钮，点击后可以弹窗显示老师的课表。

### 排课组件
1. 课程表页面或者老师管理页面上有一个排课按钮，点击后弹窗显示排课组件。
2. 排课组件有以下内容：
    - 选择学生
        - 下拉框，选择学生，显示为学生姓名（手机尾号），支持搜索
    - 选择日期范围
        - 日期选择器，选择上课日期范围，支持选择单天或多天。
    - 周视图排课，一行一条，支持多条
        - 选周几（周一到周日），选择时间段（开始时间和结束时间）
    - 提交按钮
        - 点击提交后调用接口进行排课操作，成功后更新课程表和复习课。
