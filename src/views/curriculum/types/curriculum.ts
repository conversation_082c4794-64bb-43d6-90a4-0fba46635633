// 课程数据模型
export interface CourseSchedule {
  id: string
  studentId: string
  studentName: string
  teacherId: string
  teacherName: string
  startTime: string
  endTime: string
  duration: number
  status: '待开始' | '进行中' | '已完成' | '停课'
  type: string
  subject: string
  classroom?: string
  cancelReason?: string
  cancelType?: 'teacher' | 'student'
}

// 复习课数据模型
export interface ReviewPlan {
  id: string
  studentId: string
  studentName: string
  reviewType: 'daily' | 'weekly' | 'monthly'
  wordCount: number
  scheduledDate: string
  status: '待开始' | '进行中' | '已完成'
  courseId: string
}

// 排课请求数据模型
export interface ScheduleRequest {
  studentId: string
  teacherId: string
  dateRange: [string, string]
  weeklySchedules: {
    dayOfWeek: number // 0-6, 0为周日
    startTime: string
    endTime: string
  }[]
  duration: number
  subject: string
  type: string
}

// 学生数据模型
export interface Student {
  id: string
  name: string
  phone: string
  avatar?: string
  grade?: string
  status: 'active' | 'inactive'
}

// 课程状态枚举
export enum CourseStatus {
  待开始 = '待开始',
  进行中 = '进行中',
  已完成 = '已完成',
  CANCELLED = '停课'
}

// 复习状态枚举
export enum ReviewStatus {
  待开始 = '待开始',
  进行中 = '进行中',
  已完成 = '已完成'
}

// 视图类型
export type ViewType = 'week' | 'month'

// 课程操作类型
export interface CourseAction {
  type: 'start' | 'cancel' | 'end'
  courseId: string
  data?: any
}

// 停课数据
export interface CancelCourseData {
  reason: string
  type: 'teacher' | 'student'
}