# 排课和课表功能技术设计方案

## 项目技术栈分析结果

### 1. 技术栈组成
- **前端框架**: Vue 3.3.9 + Composition API
- **UI框架**: Element Plus 2.4.3
- **状态管理**: Pinia 2.1.7
- **路由管理**: Vue Router 4.2.5
- **HTTP客户端**: Axios 0.27.2
- **构建工具**: Vite 5.0.4
- **样式预处理**: Sass 1.69.5

### 2. 项目架构特点
- 基于RuoYi-Vue框架的教育管理系统
- 采用Layout布局 + 动态路由的架构模式
- API调用使用统一的request工具
- 组件化开发，大量使用Drawer抽屉组件
- 权限控制基于角色和菜单权限

### 3. 现有代码模式
- 组件采用`<script setup>`语法
- 大量使用Element Plus组件和图标
- API接口集中在`src/api`目录管理
- 使用TypeScript定义接口类型

## 排课和课表功能技术设计方案

### 架构概览

```mermaid
graph TB
    A[课表管理模块] --> B[课表页面]
    A --> C[排课组件]
    A --> D[API接口层]
    A --> E[数据模型]
    
    B --> B1[周视图]
    B --> B2[月视图]
    B --> B3[复习课]
    
    C --> C1[学生选择]
    C --> C2[日期选择]
    C --> C3[时间设置]
    
    D --> D1[课表接口]
    D --> D2[排课接口]
    D --> D3[复习接口]
    
    E --> E1[课程数据模型]
    E --> E2[复习数据模型]
```

### 1. 文件结构设计

```
src/views/curriculum/
├── index.vue                    # 课表主页面
├── components/
│   ├── ScheduleCalendar.vue     # 课表日历组件
│   ├── WeekView.vue             # 周视图组件
│   ├── MonthView.vue            # 月视图组件
│   ├── CourseCard.vue           # 课程卡片组件
│   ├── ReviewPanel.vue          # 复习课面板
│   ├── ReviewItem.vue           # 复习项目组件
│   ├── ScheduleDialog.vue       # 排课弹窗组件
│   └── CourseStatusTag.vue      # 课程状态标签组件
├── composables/
│   ├── useSchedule.js           # 课表逻辑组合函数
│   ├── useReview.js             # 复习逻辑组合函数
│   └── useCourseActions.js      # 课程操作逻辑
└── types/
    └── curriculum.ts            # 课表相关类型定义

src/api/curriculum/
├── index.ts                     # 课表相关API
├── schedule.ts                  # 排课API
├── review.ts                    # 复习API
└── types.ts                     # API类型定义
```

### 2. 数据模型设计

```typescript
// 课程数据模型
interface CourseSchedule {
  id: string
  studentId: string
  studentName: string
  teacherId: string
  teacherName: string
  startTime: string
  endTime: string
  duration: number
  status: '待开始' | '进行中' | '已完成' | '停课'
  type: string
  subject: string
  classroom?: string
  cancelReason?: string
  cancelType?: 'teacher' | 'student'
}

// 复习课数据模型
interface ReviewPlan {
  id: string
  studentId: string
  studentName: string
  reviewType: 'daily' | 'weekly' | 'monthly'
  wordCount: number
  scheduledDate: string
  status: '待开始' | '进行中' | '已完成'
  courseId: string
}

// 排课请求数据模型
interface ScheduleRequest {
  studentId: string
  teacherId: string
  dateRange: [string, string]
  weeklySchedules: {
    dayOfWeek: number // 0-6, 0为周日
    startTime: string
    endTime: string
  }[]
  duration: number
  subject: string
  type: string
}

// 学生数据模型
interface Student {
  id: string
  name: string
  phone: string
  avatar?: string
  grade?: string
  status: 'active' | 'inactive'
}
```

### 3. API接口设计

```typescript
// 课表相关API
export interface CurriculumAPI {
  // 获取课表数据
  getSchedule(params: {
    teacherId?: string
    studentId?: string
    startDate: string
    endDate: string
    viewType: 'week' | 'month'
  }): Promise<CourseSchedule[]>

  // 排课
  createSchedule(data: ScheduleRequest): Promise<void>

  // 开始上课
  startCourse(courseId: string): Promise<void>

  // 停课
  cancelCourse(courseId: string, data: {
    reason: string
    type: 'teacher' | 'student'
  }): Promise<void>

  // 结束上课
  endCourse(courseId: string): Promise<void>

  // 获取复习课
  getReviewPlans(params: {
    teacherId?: string
    studentId?: string
    date?: string
    type: 'today' | 'future'
  }): Promise<ReviewPlan[]>

  // 开始复习
  startReview(reviewId: string): Promise<void>

  // 完成复习
  completeReview(reviewId: string): Promise<void>

  // 获取学生列表
  getStudents(params: {
    teacherId?: string
    keyword?: string
  }): Promise<Student[]>
}
```

### 4. 组件架构设计

```mermaid
graph TB
    A[curriculum/index.vue] --> B[ScheduleCalendar.vue]
    A --> C[ReviewPanel.vue]
    A --> D[ScheduleDialog.vue]
    
    B --> B1[WeekView.vue]
    B --> B2[MonthView.vue]
    
    B1 --> E[CourseCard.vue]
    B2 --> E
    E --> F[CourseStatusTag.vue]
    
    C --> G[ReviewItem.vue]
    
    D --> H[学生选择组件]
    D --> I[日期范围选择]
    D --> J[时间设置组件]
```

### 5. 路由配置方案

```javascript
// 在router/index.js中添加
{
  path: '/curriculum',
  component: Layout,
  redirect: '/curriculum/schedule',
  name: 'Curriculum',
  meta: {
    title: '课程管理',
    icon: 'schedule'
  },
  children: [
    {
      path: 'schedule',
      component: () => import('@/views/curriculum/index.vue'),
      name: 'Schedule',
      meta: { 
        title: '课表管理', 
        icon: 'calendar',
        permissions: ['curriculum:schedule:view']
      }
    }
  ]
}
```

### 6. 状态管理方案

使用Pinia创建课表状态管理：

```javascript
// stores/curriculum.js
import { defineStore } from 'pinia'

export const useCurriculumStore = defineStore('curriculum', () => {
  const schedules = ref([])
  const reviewPlans = ref([])
  const currentView = ref('week')
  const selectedDate = ref(new Date())
  const loading = ref(false)
  
  // 获取课表数据
  const fetchSchedules = async (params) => {
    loading.value = true
    try {
      const response = await curriculumAPI.getSchedule(params)
      schedules.value = response.data
    } catch (error) {
      console.error('获取课表数据失败:', error)
    } finally {
      loading.value = false
    }
  }
  
  // 更新课程状态
  const updateCourseStatus = (courseId, status) => {
    const course = schedules.value.find(item => item.id === courseId)
    if (course) {
      course.status = status
    }
  }
  
  // 获取复习课
  const fetchReviewPlans = async (params) => {
    try {
      const response = await curriculumAPI.getReviewPlans(params)
      reviewPlans.value = response.data
    } catch (error) {
      console.error('获取复习课失败:', error)
    }
  }
  
  return {
    schedules,
    reviewPlans,
    currentView,
    selectedDate,
    loading,
    fetchSchedules,
    updateCourseStatus,
    fetchReviewPlans
  }
})
```

### 7. 核心功能实现要点

#### 7.1 课表日历组件
- 支持周视图和月视图切换
- 课程时间段的动态计算和渲染
- 拖拽排课功能（可选）
- 响应式布局适配

#### 7.2 课程操作功能
- 开始上课：调用API并在新标签页打开上课页面
- 停课功能：弹窗输入停课原因和类型
- 结束上课：确认后调用API更新状态

#### 7.3 复习课管理
- 今日/未来标签页切换
- 复习项目列表展示
- 复习操作和状态更新

#### 7.4 排课组件
- 学生选择（支持搜索）
- 日期范围选择
- 周时间段设置
- 批量排课提交

### 8. 权限控制设计

```javascript
// 权限控制配置
const permissions = {
  // 管理员权限
  admin: [
    'curriculum:schedule:view',      // 查看所有课表
    'curriculum:schedule:create',    // 排课
    'curriculum:schedule:edit',      // 编辑课程
    'curriculum:schedule:delete',    // 删除课程
    'curriculum:review:manage'       // 管理复习课
  ],
  // 老师权限
  teacher: [
    'curriculum:schedule:view:own',  // 查看自己的课表
    'curriculum:schedule:create',    // 排课
    'curriculum:schedule:operate',   // 课程操作
    'curriculum:review:view:own'     // 查看自己的复习课
  ],
  // 学生权限
  student: [
    'curriculum:schedule:view:own',  // 查看自己的课表
    'curriculum:review:view:own'     // 查看自己的复习课
  ]
}
```

### 9. 实施步骤

```mermaid
gantt
    title 排课和课表功能开发计划
    dateFormat  YYYY-MM-DD
    section 第一阶段
    数据模型设计     :done, stage1, 2025-01-01, 1d
    API接口设计      :done, stage2, 2025-01-02, 1d
    基础组件开发     :active, stage3, 2025-01-03, 3d
    
    section 第二阶段
    课表页面开发     :stage4, 2025-01-06, 4d
    排课组件开发     :stage5, 2025-01-10, 3d
    
    section 第三阶段
    复习功能开发     :stage6, 2025-01-13, 3d
    权限控制集成     :stage7, 2025-01-16, 2d
    
    section 第四阶段
    联调测试        :stage8, 2025-01-18, 3d
    优化部署        :stage9, 2025-01-21, 2d
```

#### 详细实施步骤：

**第一阶段：基础设施（3天）**
1. 创建文件目录结构
2. 定义数据模型和类型
3. 设计API接口规范
4. 配置路由和权限

**第二阶段：核心功能（7天）**
1. 开发课表主页面布局
2. 实现ScheduleCalendar组件
3. 开发周视图和月视图
4. 实现CourseCard组件
5. 开发排课弹窗组件

**第三阶段：扩展功能（5天）**
1. 开发复习课面板
2. 实现课程操作功能
3. 集成权限控制
4. 添加Pinia状态管理

**第四阶段：测试优化（5天）**
1. API接口联调
2. 功能测试和bug修复
3. 性能优化
4. 代码review和文档

### 10. 技术特色和优势

1. **组件化设计**: 高度复用的组件架构，便于维护和扩展
2. **响应式布局**: 支持不同屏幕尺寸，提供良好的用户体验
3. **权限控制**: 基于角色的精细化权限管理
4. **状态管理**: 使用Pinia进行高效的状态管理
5. **类型安全**: 完整的TypeScript类型定义
6. **API设计**: RESTful风格的API接口设计
7. **可扩展性**: 预留扩展接口，便于后续功能添加

### 11. 性能优化策略

1. **懒加载**: 组件和路由懒加载
2. **数据缓存**: 课表数据的智能缓存
3. **虚拟滚动**: 大量数据列表的虚拟滚动
4. **防抖节流**: 搜索和API调用的防抖处理
5. **代码分割**: Webpack代码分割优化

### 12. 错误处理和用户体验

1. **Loading状态**: 数据加载时的loading提示
2. **错误提示**: 友好的错误提示信息
3. **重试机制**: 网络请求失败的重试机制
4. **表单验证**: 完整的表单验证和提示
5. **操作确认**: 重要操作的确认提示

这个技术方案充分考虑了项目的现有架构和技术栈，确保新功能能够无缝集成到现有系统中，同时保持代码的可维护性和扩展性。