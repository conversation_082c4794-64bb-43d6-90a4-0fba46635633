<template>
  <div class="review-panel">
    <!-- 面板头部 -->
    <div class="panel-header">
      <h3 class="panel-title">
        <el-icon class="title-icon"><Reading /></el-icon>
        复习课
      </h3>
      <el-button
        type="primary"
        size="small"
        :icon="Refresh"
        @click="handleRefresh"
        :loading="loading"
        circle
      />
    </div>

    <!-- 标签页切换 -->
    <el-tabs v-model="activeTab" class="review-tabs" @tab-change="handleTabChange">
      <el-tab-pane name="today">
        <template #label>
          <span class="tab-label">
            今日
            <el-badge
              v-if="todayCount > 0"
              :value="todayCount"
              class="tab-badge"
              :max="99"
            />
          </span>
        </template>

        <!-- 今日复习列表 -->
        <div class="review-list" v-loading="loading">
          <div v-if="todayReviews.length === 0" class="empty-state">
            <el-empty description="今日暂无复习课" :image-size="100">
              <template #image>
                <el-icon class="empty-icon"><Calendar /></el-icon>
              </template>
            </el-empty>
          </div>

          <ReviewItem
            v-for="review in todayReviews"
            :key="review.id"
            :review="review"
            @review-start="handleReviewStart"
            @review-complete="handleReviewComplete"
            @review-view="handleReviewView"
          />
        </div>
      </el-tab-pane>

      <el-tab-pane name="future">
        <template #label>
          <span class="tab-label">
            未完成
            <el-badge
              v-if="futureCount > 0"
              :value="futureCount"
              class="tab-badge"
              :max="99"
            />
          </span>
        </template>

        <!-- 未来复习列表 -->
        <div class="review-list" v-loading="loading">
          <div v-if="futureReviews.length === 0" class="empty-state">
            <el-empty description="暂无未来复习课" :image-size="100">
              <template #image>
                <el-icon class="empty-icon"><Clock /></el-icon>
              </template>
            </el-empty>
          </div>

          <!-- 按日期分组显示 -->
          <div v-for="group in groupedFutureReviews" :key="group.date" class="date-group">
            <div class="date-header">
              <el-icon class="date-icon"><Calendar /></el-icon>
              <span class="date-text">{{ formatGroupDate(group.date) }}</span>
              <span class="count-text">({{ group.reviews.length }}项)</span>
            </div>

            <ReviewItem
              v-for="review in group.reviews"
              :key="review.id"
              :review="review"
              @review-start="handleReviewStart"
              @review-complete="handleReviewComplete"
              @review-view="handleReviewView"
            />
          </div>
        </div>
      </el-tab-pane>
    </el-tabs>

    <!-- 快速统计 -->
    <div class="review-stats" v-if="showStats">
      <div class="stat-item">
        <span class="stat-label">待复习</span>
        <span class="stat-value 待开始">{{ WaittingCount }}</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">进行中</span>
        <span class="stat-value 进行中">{{ inProcessingCount }}</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">已完成</span>
        <span class="stat-value 已完成">{{ completedCount }}</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from "vue";
import { Reading, Refresh, Calendar, Clock } from "@element-plus/icons-vue";
import { useCurriculumStore } from "@/stores/curriculum";
import ReviewItem from "./ReviewItem.vue";

const props = defineProps({
  teacherId: {
    type: String,
    default: "",
  },
  studentId: {
    type: String,
    default: "",
  },
  showStats: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(["reviewStart", "reviewComplete", "reviewView"]);

const curriculumStore = useCurriculumStore();
const activeTab = ref("today");
const loading = ref(false);

// 计算属性
const todayReviews = computed(() => curriculumStore.todayReviews);
const futureReviews = computed(() => curriculumStore.futureReviews);

const todayCount = computed(() => todayReviews.value.length);
const futureCount = computed(() => futureReviews.value.length);

// 按日期分组的未来复习 - 修复：支持统一数据结构
const groupedFutureReviews = computed(() => {
  const groups = {};

  futureReviews.value.forEach((review) => {
    // 修复：支持统一数据结构，优先使用scheduledDate，其次使用startTime
    const reviewDate = review.scheduledDate || review.startTime;
    if (!reviewDate) return;

    const date = new Date(reviewDate).toDateString();
    console.log("🔧 [修复] ReviewPanel 分组复习课:", {
      reviewId: review.id,
      scheduledDate: review.scheduledDate,
      startTime: review.startTime,
      groupDate: date,
    });

    if (!groups[date]) {
      groups[date] = {
        date: date,
        reviews: [],
      };
    }
    groups[date].reviews.push(review);
  });

  // 按日期排序
  return Object.values(groups).sort((a, b) => new Date(a.date) - new Date(b.date));
});

// 统计数据
const WaittingCount = computed(() => {
  const allReviews = [...todayReviews.value, ...futureReviews.value];
  return allReviews.filter((review) => review.status === "待开始").length;
});

const inProcessingCount = computed(() => {
  const allReviews = [...todayReviews.value, ...futureReviews.value];
  return allReviews.filter((review) => review.status === "进行中").length;
});

const completedCount = computed(() => {
  const allReviews = [...todayReviews.value, ...futureReviews.value];
  return allReviews.filter((review) => review.status === "已完成").length;
});

// 方法
const fetchReviewData = async (type = "today") => {
  loading.value = true;
  try {
    const params = {
      type: "复习课",
      teacherId: props.teacherId,
      studentId: props.studentId,
      viewType: type === "today" ? "today" : "future",
    };

    if (type === "today") {
      params.date = new Date().toISOString().split("T")[0];
    }
    await curriculumStore.fetchReviewPlans(params);

    console.log("🔍 [诊断] 复习数据获取完成");
    console.log("🔍 [诊断] todayReviews数量:", todayReviews.value.length);
    console.log("🔍 [诊断] futureReviews数量:", futureReviews.value.length);
  } finally {
    loading.value = false;
  }
};

const handleTabChange = (tabName) => {
  fetchReviewData(tabName);
};

const handleRefresh = () => {
  fetchReviewData(activeTab.value);
};

const formatGroupDate = (dateString) => {
  const date = new Date(dateString);
  const now = new Date();
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
  const targetDate = new Date(date.getFullYear(), date.getMonth(), date.getDate());

  const diffDays = Math.floor((targetDate - today) / (1000 * 60 * 60 * 24));

  if (diffDays === 1) {
    return "明天";
  } else if (diffDays === 2) {
    return "后天";
  } else if (diffDays <= 7) {
    const weekdays = ["周日", "周一", "周二", "周三", "周四", "周五", "周六"];
    return weekdays[date.getDay()];
  } else {
    return date.toLocaleDateString("zh-CN", {
      month: "2-digit",
      day: "2-digit",
    });
  }
};

// 事件处理
const handleReviewStart = (review) => {
  emit("reviewStart", review);
};

const handleReviewComplete = (review) => {
  emit("reviewComplete", review);
};

const handleReviewView = (review) => {
  emit("reviewView", review);
};

// 监听属性变化
watch(
  [() => props.teacherId, () => props.studentId],
  () => {
    if (props.teacherId || props.studentId) {
      fetchReviewData(activeTab.value);
    }
  },
  { immediate: true }
);

onMounted(() => {
  fetchReviewData("today");
});
</script>

<style lang="scss" scoped>
.review-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;

  .panel-title {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    display: flex;
    align-items: center;

    .title-icon {
      margin-right: 8px;
      font-size: 20px;
    }
  }
}

.review-tabs {
  flex: 1;
  display: flex;
  flex-direction: column;

  :deep(.el-tabs__header) {
    margin: 0;
    background: #f8f9fa;
    padding: 0 20px;
  }

  :deep(.el-tabs__nav-wrap) {
    &::after {
      display: none;
    }
  }

  :deep(.el-tabs__item) {
    font-weight: 500;
    color: #6b7280;

    &.is-active {
      color: #667eea;
    }
  }

  :deep(.el-tabs__active-bar) {
    background-color: #667eea;
  }

  :deep(.el-tabs__content) {
    flex: 1;
    padding: 0;
  }

  :deep(.el-tab-pane) {
    height: 100%;
  }
}

.tab-label {
  display: flex;
  align-items: center;
  gap: 6px;
}

.tab-badge {
  :deep(.el-badge__content) {
    transform: translateX(50%);
  }
}

.review-list {
  height: 100%;
  padding: 16px 20px;
  overflow-y: auto;

  // 自定义滚动条
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;

    &:hover {
      background: #a8a8a8;
    }
  }
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;

  .empty-icon {
    font-size: 48px;
    color: #d1d5db;
  }
}

.date-group {
  margin-bottom: 24px;

  &:last-child {
    margin-bottom: 0;
  }
}

.date-header {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  background: #f8fafc;
  border-radius: 8px;
  margin-bottom: 12px;
  border-left: 4px solid #667eea;

  .date-icon {
    color: #667eea;
    margin-right: 8px;
  }

  .date-text {
    font-weight: 600;
    color: #374151;
  }

  .count-text {
    margin-left: 8px;
    color: #6b7280;
    font-size: 14px;
  }
}

.review-stats {
  display: flex;
  justify-content: space-around;
  padding: 16px 20px;
  background: #f8f9fa;
  border-top: 1px solid #e5e7eb;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;

  .stat-label {
    font-size: 12px;
    color: #6b7280;
    font-weight: 500;
  }

  .stat-value {
    font-size: 18px;
    font-weight: 700;

    &.待开始 {
      color: #f59e0b;
    }

    &.进行中 {
      color: #3b82f6;
    }

    &.已完成 {
      color: #10b981;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .review-panel {
    height: auto;
    max-height: 60vh;
  }

  .panel-header {
    padding: 12px 16px;

    .panel-title {
      font-size: 16px;
    }
  }

  .review-list {
    padding: 12px 16px;
    max-height: 400px;
  }

  .review-stats {
    padding: 12px 16px;
  }
}
</style>
