<template>
  <div 
    class="course-card" 
    :class="[`status-${course.status}`, { 'is-current': isCurrent }]"
    @click="handleCardClick"
  >
    <!-- 课程头部信息 -->
    <div class="course-header">
      <div class="course-time">
        <div class="course-type-student">
          【{{ course.type || course.specification || '课程' }}】
        </div>
        <span class="start-time">{{ formatTime(course.startTime) }}</span>
        <span class="time-separator">-</span>
        <span class="end-time">{{ formatTime(course.endTime) }}</span>
      </div>

      <span class="course-meta duration">{{ course.duration }}分钟</span>
      <CourseStatusTag :status="course.status" />
    </div>

    <!-- 课程主要信息 -->
    <div class="course-content">
      <!-- 周视图特殊显示格式 -->
      <div v-if="isWeekView" class="week-view-content">
        <div class="course-type-student">
          【{{ course.type || course.specification || '课程' }}】{{ course.studentName }}
        </div>
        <div class="course-subject">
          {{ course.specification || course.subject }}
        </div>
      </div>
      
      <!-- 普通视图显示格式 -->
      <template v-else>
        <div class="student-info" v-if="showStudentName">
          <el-icon class="info-icon"><User /></el-icon>
          学生：
          <span class="student-name">{{ course.studentName }}</span>
        </div>
        
        <div class="teacher-info" v-if="showTeacherName">
          <el-icon class="info-icon"><Avatar /></el-icon>
          老师：
          <span class="teacher-name">{{ course.teacherName }}</span>
        </div>

        <!-- <div class="course-meta">
          <span class="subject">{{ course.subject }}</span>
          <span class="course-type" v-if="course.type || course.specification">
            {{ course.type || course.specification }}
          </span>
        </div> -->
      </template>
    </div>

    <!-- 课程操作按钮 -->
    <div class="course-actions" v-if="showActions" @click.stop>
      <el-button 
        v-if="course.status === '待开始'" 
        type="primary" 
        size="small"
        :icon="VideoPlay"
        @click="handleStartCourse"
        :loading="loading.start"
      >
        开始上课
      </el-button>

      <el-button 
        v-if="course.status === '进行中'" 
        type="primary" 
        size="small"
        :icon="VideoPlay"
        @click="handleStartCourse"
        :loading="loading.start"
      >
        进入课堂
      </el-button>

      <el-button 
        v-if="course.status === '已完成'" 
        type="primary" 
        size="small"
        :icon="VideoPlay"
        @click="handleStartCourse"
        :loading="loading.start"
      >
        查看课程
      </el-button>

      <el-button 
        v-if="canCancel" 
        type="danger" 
        size="small"
        :icon="Close"
        @click="handleCancelCourse"
        :loading="loading.cancel"
      >
        停课
      </el-button>
    </div>

    <!-- 停课原因（如果已停课） -->
    <div class="cancel-reason" v-if="course.status === '停课' && course.cancelReason">
      <el-icon class="reason-icon"><Warning /></el-icon>
      <span class="reason-text">{{ course.cancelReason }}</span>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { ElMessageBox, ElMessage } from 'element-plus'
import { 
  User, 
  Avatar, 
  VideoPlay, 
  Check, 
  Close, 
  Warning 
} from '@element-plus/icons-vue'
import { useCurriculumStore } from '@/stores/curriculum'
import CourseStatusTag from './CourseStatusTag.vue'

const props = defineProps({
  course: {
    type: Object,
    required: true
  },
  showStudentName: {
    type: Boolean,
    default: true
  },
  showTeacherName: {
    type: Boolean,
    default: true
  },
  showActions: {
    type: Boolean,
    default: true
  },
  isWeekView: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['courseClick', 'courseStart', 'courseEnd', 'courseCancel'])

const curriculumStore = useCurriculumStore()

// 加载状态
const loading = ref({
  start: false,
  end: false,
  cancel: false
})

// 是否可以停课
const canCancel = computed(() => {
  return ['待开始', '进行中'].includes(props.course.status)
})

// 是否是当前时间的课程
const isCurrent = computed(() => {
  if (props.course.status !== '进行中') return false
  
  const now = new Date()
  const startTime = new Date(props.course.startTime)
  const endTime = new Date(props.course.endTime)
  
  return now >= startTime && now <= endTime
})

// 格式化时间
const formatTime = (timeString) => {
  const date = new Date(timeString)
  return date.toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit',
    hour12: false
  })
}

// 处理卡片点击
const handleCardClick = () => {
  emit('courseClick', props.course)
}

// 处理开始上课
const handleStartCourse = async () => {
  loading.value.start = true
  try {
    const success = await curriculumStore.startCourse(props.course.id)
    if (success) {
      emit('courseStart', props.course)
    }
  } finally {
    loading.value.start = false
  }
}

// 处理结束上课
const handleEndCourse = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要结束这节课吗？',
      '确认结束上课',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    loading.value.end = true
    const success = await curriculumStore.endCourse(props.course.id)
    if (success) {
      emit('courseEnd', props.course)
    }
  } catch {
    // 用户取消操作
  } finally {
    loading.value.end = false
  }
}

// 处理停课
const handleCancelCourse = async () => {
  try {
    // 创建自定义停课表单HTML
    const cancelFormHtml = `
      <div class="cancel-course-form">
        <div class="form-group">
          <label class="form-label">停课类型：</label>
          <div class="radio-group">
            <label class="radio-item">
              <input type="radio" name="cancelType" value="teacher" checked>
              <span class="radio-text">老师原因</span>
            </label>
            <label class="radio-item">
              <input type="radio" name="cancelType" value="student">
              <span class="radio-text">学生原因</span>
            </label>
          </div>
        </div>
        <div class="form-group">
          <label class="form-label">停课说明：</label>
          <textarea
            id="cancelReason"
            class="form-textarea"
            placeholder="请详细说明停课原因..."
            rows="3"
          ></textarea>
        </div>
      </div>
    `

    let cancelData = { type: 'teacher', reason: '' }

    const { value } = await ElMessageBox({
      title: '停课确认',
      message: cancelFormHtml,
      dangerouslyUseHTMLString: true,
      confirmButtonText: '确定停课',
      cancelButtonText: '取消',
      type: 'warning',
      customClass: 'cancel-course-dialog',
      beforeClose: (action, instance, done) => {
        if (action === 'confirm') {
          try {
            // 直接查询DOM，因为表单已经渲染在当前页面中
            const selectedType = document.querySelector('.cancel-course-dialog input[name="cancelType"]:checked')?.value
            const reason = document.querySelector('.cancel-course-dialog #cancelReason')?.value?.trim()
            
            if (!reason) {
              ElMessage.warning('请填写停课说明')
              return false
            }
            
            // 更新外部的cancelData变量
            cancelData = {
              type: selectedType || 'teacher',
              reason: reason
            }
          } catch (error) {
            console.error('获取停课表单数据失败:', error)
            ElMessage.error('获取表单数据失败')
            return false
          }
        }
        done()
      }
    })

    loading.value.cancel = true

    console.log('停课数据:', cancelData)

    const success = await curriculumStore.cancelCourse(props.course.id, cancelData)
    if (success) {
      emit('courseCancel', props.course)
    }
  } catch (error) {
    console.log('用户取消停课操作或发生错误:', error)
  } finally {
    loading.value.cancel = false
  }
}
</script>

<style lang="scss" scoped>
.course-card {
  background: #fff;
  border-radius: 12px;
  padding: 16px;
  margin: 8px 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-left: 4px solid #e0e0e0;
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  overflow: hidden;

  &:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
  }

  &.is-current {
    border-left-color: #f39c12;
    background: linear-gradient(135deg, #fff8e1 0%, #ffffff 100%);
    
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 3px;
      background: linear-gradient(90deg, #f39c12, #e67e22);
      animation: progress 3s ease-in-out infinite;
    }
  }

  // 不同状态的样式
  &.status-待开始 {
    border-left-color: #909399;
    
    .course-header {
      background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    }
  }

  &.status-进行中 {
    border-left-color: #f39c12;
    
    .course-header {
      background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%);
    }
  }

  &.status-已完成 {
    border-left-color: #67c23a;
    
    .course-header {
      background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    }
  }

  &.status-cancelled {
    border-left-color: #f56c6c;
    opacity: 0.8;
    
    .course-header {
      background: linear-gradient(135deg, #f8d7da 0%, #f1b2b5 100%);
    }
  }
}

.course-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding: 8px 12px;
  border-radius: 8px;
  background: #f8f9fa;
}

.course-time {
  display: flex;
  align-items: center;
  font-weight: 600;
  color: #2c3e50;
  
  .start-time, .end-time {
    font-size: 16px;
  }
  
  .time-separator {
    margin: 0 8px;
    color: #7f8c8d;
  }
}

.course-content {
  margin-bottom: 12px;
}

.student-info, .teacher-info {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  color: #34495e;
  
  .info-icon {
    margin-right: 6px;
    color: #3498db;
  }
  
  .student-name, .teacher-name {
    font-weight: 500;
  }
}

.course-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #7f8c8d;
  font-size: 14px;
  flex-wrap: wrap;
  gap: 8px;
  
  .subject {
    font-weight: 500;
    color: #2980b9;
  }
  
  .duration {
    background: #ecf0f1;
    padding: 2px 8px;
    border-radius: 12px;
  }
  
  .course-type {
    background: #e3f2fd;
    color: #1976d2;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
  }
}

// 周视图特殊样式
.week-view-content {
  .course-type-student {
    font-size: 12px;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 4px;
    line-height: 1.2;
  }
  
  .course-subject {
    font-size: 10px;
    color: #3498db;
    background: #ecf5ff;
    padding: 2px 6px;
    border-radius: 8px;
    font-weight: 500;
    display: inline-block;
  }
}

.course-actions {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  
  .el-button {
    flex: 1;
    min-width: 80px;
  }
}

.cancel-reason {
  margin-top: 12px;
  padding: 8px 12px;
  background: #fdf2f2;
  border-radius: 6px;
  border-left: 3px solid #f56c6c;
  display: flex;
  align-items: center;
  
  .reason-icon {
    color: #f56c6c;
    margin-right: 6px;
  }
  
  .reason-text {
    color: #721c24;
    font-size: 14px;
  }
}

@keyframes progress {
  0% { transform: translateX(-100%); }
  50% { transform: translateX(0%); }
  100% { transform: translateX(100%); }
}

// 响应式设计
@media (max-width: 768px) {
  .course-card {
    padding: 12px;
  }
  
  .course-time {
    .start-time, .end-time {
      font-size: 14px;
    }
  }
  
  .course-actions {
    .el-button {
      font-size: 12px;
      padding: 4px 8px;
    }
  }
}
</style>

<style>
.cancel-course-dialog .el-message-box__content {
  padding-bottom: 20px;
}

.cancel-course-dialog .el-textarea__inner {
  min-height: 80px;
}

/* 停课表单样式 */
.cancel-course-form {
  text-align: left;
  padding: 10px 0;
}

.cancel-course-form .form-group {
  margin-bottom: 16px;
}

.cancel-course-form .form-label {
  display: block;
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
}

.cancel-course-form .radio-group {
  display: flex;
  gap: 16px;
}

.cancel-course-form .radio-item {
  display: flex;
  align-items: center;
  cursor: pointer;
  font-size: 14px;
  color: #666;
}

.cancel-course-form .radio-item input[type="radio"] {
  margin-right: 6px;
  width: 16px;
  height: 16px;
  cursor: pointer;
}

.cancel-course-form .radio-text {
  cursor: pointer;
  user-select: none;
}

.cancel-course-form .form-textarea {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #dcdfe6;
  border-radius: 6px;
  font-size: 14px;
  line-height: 1.4;
  color: #333;
  background-color: #fff;
  resize: vertical;
  min-height: 80px;
  font-family: inherit;
}

.cancel-course-form .form-textarea:focus {
  outline: none;
  border-color: #409eff;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

.cancel-course-form .form-textarea::placeholder {
  color: #c0c4cc;
}
</style>