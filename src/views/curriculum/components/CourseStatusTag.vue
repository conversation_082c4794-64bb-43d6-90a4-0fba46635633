<template>
  <el-tag
    :type="tagType"
    :effect="effect"
    size="small"
    class="course-status-tag"
  >
    <el-icon class="status-icon">
      <component :is="statusIcon" />
    </el-icon>
    {{ statusText }}
  </el-tag>
</template>

<script setup>
import { computed } from 'vue'
import { Clock, VideoPlay, Check, Close } from '@element-plus/icons-vue'

const props = defineProps({
  status: {
    type: String,
    required: true,
    validator: (value) => ['待开始', '进行中', '已完成', '停课'].includes(value)
  }
})

// 状态对应的标签类型
const tagType = computed(() => {
  const typeMap = {
    待开始: 'info',
    进行中: 'warning',
    已完成: 'success',
    cancelled: 'danger'
  }
  return typeMap[props.status] || 'info'
})

// 标签效果
const effect = computed(() => {
  return props.status === '进行中' ? 'dark' : 'light'
})

// 状态文本
const statusText = computed(() => {
  const textMap = {
    待开始: '待开始',
    进行中: '进行中',
    已完成: '已完成',
    cancelled: '已停课'
  }
  return textMap[props.status] || '未知'
})

// 状态图标
const statusIcon = computed(() => {
  const iconMap = {
    待开始: Clock,
    进行中: VideoPlay,
    已完成: Check,
    cancelled: Close
  }
  return iconMap[props.status] || Clock
})
</script>

<style lang="scss" scoped>
.course-status-tag {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  font-weight: 500;
  
  .status-icon {
    font-size: 12px;
  }
}

// 不同状态的特殊样式
.course-status-tag.el-tag--warning.is-dark {
  background: linear-gradient(135deg, #f39c12, #e67e22);
  border-color: #e67e22;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    box-shadow: 0 0 0 0 rgba(243, 156, 18, 0.7);
  }
  50% {
    box-shadow: 0 0 0 4px rgba(243, 156, 18, 0);
  }
}
</style>