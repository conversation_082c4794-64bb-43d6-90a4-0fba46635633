<template>
  <el-drawer
    v-model="visible"
    title="单词测验结果"
    size="500px"
    :with-header="false"
    direction="rtl"
    class="word-test-drawer"
  >
    <div class="word-test-container" v-loading="disabled">
      <!-- 抽屉头部 -->
      <div class="word-test-header">
        <div class="header-content">
          <div class="header-title">
            <el-icon class="header-icon"><TrendCharts /></el-icon>
            单词测验结果
          </div>
          <div class="header-subtitle">
            <span class="sparkle">✨</span>
            每一次的进步，都值得被嘉奖
            <span class="sparkle">✨</span>
          </div>
        </div>
        <div class="header-actions">
          <el-button
            type="primary"
            size="large"
            class="test-button"
            :loading="disabled"
            @click="startWordTest"
          >
            <el-icon><RefreshRight /></el-icon>
            再次测验
          </el-button>
        </div>
      </div>

      <!-- 测验内容区域 -->
      <div class="word-test-content">
        <!-- 测验结果卡片 -->
        <div
          v-for="(detail, detailIndex) in wordTestedList"
          :key="detailIndex"
          class="test-result-card"
        >
          <!-- 测验基本信息 -->
          <div class="test-header">
            <div class="test-date">
              <el-icon><Calendar /></el-icon>
              <span>{{ detail.testedTime }}</span>
              <!-- <div
                class="test-status-tag"
                :class="{
                  'status-in-progress': detail.status === '进行中',
                  'status-completed': detail.status === '已完成',
                }"
              >
                <el-icon v-if="detail.status === '进行中'"><Loading /></el-icon>
                <el-icon v-else><CircleCheck /></el-icon>
                {{ detail.status }}
              </div> -->
            </div>
          </div>

          <!-- 成绩概览区域 -->
          <div class="score-overview">
            <div class="score-stats">
              <div class="stat-item">
                <div class="stat-icon">📝</div>
                <div class="stat-value">{{ detail.testedWordNum }}</div>
                <div class="stat-label">测试单词</div>
              </div>
              <div class="stat-item">
                <div class="stat-icon">✅</div>
                <div class="stat-value">{{ detail.successWordNum }}</div>
                <div class="stat-label">正确单词</div>
              </div>
              <div class="stat-item">
                <div class="stat-icon">🎯</div>
                <div class="stat-value">{{ detail.successRate }}</div>
                <div class="stat-label">正确率</div>
              </div>
            </div>
          </div>

          <!-- 词汇水平展示 -->
          <div class="vocabulary-section">
            <div class="level-display">
              <div class="level-title">
                <el-icon><Trophy /></el-icon>
                词汇水平
              </div>
              <div
                class="level-badge"
                :class="{
                  'level-low': detail.result === '低',
                  'level-medium': detail.result === '中',
                  'level-high': detail.result === '高',
                }"
              >
                {{ detail.result }}
              </div>
            </div>
            <div class="vocab-stats">
              <div class="vocab-item">
                <div class="vocab-icon">📚</div>
                <div class="vocab-info">
                  <div class="vocab-label">预估掌握词汇量</div>
                  <div class="vocab-value">{{ detail.estimatedWordNum }}</div>
                </div>
              </div>
            </div>
          </div>

          <!-- 详细分析区域 -->
          <div class="analysis-section" v-if="detail.testDetailInfo">
            <div class="section-title">
              <el-icon><DataAnalysis /></el-icon>
              详细分析
            </div>
            
            <!-- 学习进度分析 -->
            <div class="progress-analysis">
              <div class="progress-item">
                <div class="progress-label">课本单词掌握率</div>
                <el-progress 
                  :percentage="parseFloat(detail.testDetailInfo.lastSemesterWordCollectRate)" 
                  :color="getProgressColor(detail.testDetailInfo.lastSemesterWordCollectRate)"
                />
              </div>
              <div class="progress-item">
                <div class="progress-label">高分词汇掌握率</div>
                <el-progress 
                  :percentage="parseFloat(detail.testDetailInfo.specialWordCollectRate)"
                  :color="getProgressColor(detail.testDetailInfo.specialWordCollectRate)"
                />
              </div>
            </div>

            <!-- 分析结果区域 -->
            <div class="analysis-result">
              <!-- 这里可以添加其他分析结果 -->
            </div>
          </div>

          <!-- 学习建议 -->
          <div class="suggestions-section">
            <div class="suggestions-header">
              <el-icon><InfoFilled /></el-icon>
              <span>学习建议</span>
            </div>
            <div class="suggestions-content">
              <div class="suggestion-tabs">
                <div 
                  class="tab-item" 
                  :class="{ active: activeTab === 'general' }"
                  @click="activeTab = 'general'"
                >
                  课本词汇建议
                </div>
                <div 
                  class="tab-item" 
                  :class="{ active: activeTab === 'special' }"
                  @click="activeTab = 'special'"
                >
                  高分词汇建议
                </div>
                <div 
                  class="tab-item" 
                  :class="{ active: activeTab === 'plan' }"
                  @click="activeTab = 'plan'"
                >
                  学习计划
                </div>
              </div>
              <div class="tab-content" v-if="activeTab === 'general'">
                {{ detail.testDetailInfo?.lastSemesterSuggestions || '暂无' }}
              </div>
              <div class="tab-content" v-else-if="activeTab === 'special'">
                {{ detail.testDetailInfo?.specialWordSuggestions || '暂无' }}
              </div>
              <div class="tab-content" v-else-if="activeTab === 'plan'">
                <div class="plan-content">
                  
                  <!-- 建议卡片区域 -->
                  <div class="plan-cards">
                    <div class="suggestion-card" :class="{'expanded': expandedTextbook}">
                      <div class="suggestion-icon">📖</div>
                      <div class="suggestion-content">
                        <div class="suggestion-title">建议教材</div>
                        <div class="suggestion-text" :class="{'long-text': isLongText(detail.testDetailInfo?.suggestTextbookName)}">
                          {{ detail.testDetailInfo?.suggestTextbookName || '暂无' }}
                        </div>
                        <div 
                          v-if="isLongText(detail.testDetailInfo?.suggestTextbookName)" 
                          class="expand-toggle"
                          @click="expandedTextbook = !expandedTextbook"
                        >
                          {{ expandedTextbook ? '收起' : '展开' }}
                        </div>
                      </div>
                    </div>
                    <div class="suggestion-card">
                      <div class="suggestion-icon">⏰</div>
                      <div class="suggestion-content">
                        <div class="suggestion-title">建议学习时间</div>
                        <div class="suggestion-text">{{ detail.testDetailInfo?.suggestLearnTime || '暂无' }}</div>
                      </div>
                    </div>
                    <div class="suggestion-card">
                      <div class="suggestion-icon">📖</div>
                      <div class="suggestion-content">
                        <div class="suggestion-title">学习进阶建议</div>
                        <div class="suggestion-text">{{ detail.testDetailInfo?.specialWordLearnSuggestions || '暂无' }}</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </el-drawer>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
import {
  TrendCharts,
  Calendar,
  Collection,
  UserFilled,
  InfoFilled,
  Loading,
  CircleCheck,
  RefreshRight,
  Trophy,
  DataAnalysis,
} from "@element-plus/icons-vue";
import {
  WordTestInfo,
  getWordTestListApi,
  startWordTestCourseInfoApi,
} from "../../../api/course";
import { ElMessage } from "element-plus";

const props = defineProps({
  visible: {
    type: Boolean,
    required: true,
  },
  studentId: {
    type: String,
    required: true,
  },
  courseId: {
    type: String,
    required: true,
  },
});

const wordTestedList = ref<WordTestInfo[]>([]);
const disabled = ref<boolean>(false);
const activeTab = ref<string>('general'); // 控制建议标签页
const expandedTextbook = ref<boolean>(false); // 控制建议教材展开状态

// 判断文本是否过长需要展开/收起功能
const isLongText = (text: string | undefined): boolean => {
  if (!text) return false;
  return text.length > 30; // 超过30个字符视为长文本
};

const emit = defineEmits<{
  (e: "update:visible", value: boolean): void;
  (e: "close"): void;
  (e: "startLearning", nodeInfo: any): void;
}>();

onMounted(() => {
  // 初始化数据
  initData();
});

const initData = () => {
  getWordTestListApi(props.studentId).then((res:any) => {
    if (res.code == 200) {
      wordTestedList.value = res.data as WordTestInfo[];
      // 初始化时重置标签页
      activeTab.value = 'general';
    }
  });
};

// 获取进度条颜色
const getProgressColor = (percentage: string) => {
  const value = parseInt(percentage);
  if (value < 40) return "#F56C6C";
  if (value < 70) return "#E6A23C";
  return "#67C23A";
};

// 开始复习
const startWordTest = () => {
  disabled.value = true;
  startWordTestCourseInfoApi(props.studentId, props.courseId)
    .then((res) => {
      if (res.code == 200) {
        ElMessage.success("开始测验成功");
        // localStorage.setItem(COURSE_LOCALSTORAGE_INFO_KEY + courseId.value,JSON.stringify(res.data));
        emit("startLearning", res.data);
        handleClose();
      }
    })
    .finally(() => {
      disabled.value = false;
    });
};

// 关闭抽屉
const handleClose = () => {
  emit("update:visible", false);
  emit("close");
};

// 监听visible属性变化
const visible = computed({
  get: () => props.visible,
  set: (value) => {
    emit("update:visible", value);
  },
});
</script>

<style lang="scss" scoped>
.word-test-drawer {
  :deep(.el-drawer__body) {
    padding: 0;
  }
}

.word-test-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #f5f7fa;
}

.word-test-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px;
  background: linear-gradient(135deg, #409eff, #67c23a);
  color: white;

  .header-content {
    .header-title {
      display: flex;
      align-items: center;
      font-size: 24px;
      font-weight: bold;
      margin-bottom: 8px;

      .header-icon {
        margin-right: 8px;
        font-size: 24px;
      }
    }

    .header-subtitle {
      font-size: 14px;
      opacity: 0.9;
      display: flex;
      align-items: center;

      .sparkle {
        margin: 0 6px;
        animation: twinkle 1.5s infinite alternate;
      }
    }
  }

  .header-actions {
    .test-button {
      background: white;
      color: #409eff;
      border: none;
      transition: all 0.3s;
      padding: 10px 20px;
      font-size: 16px;
      border-radius: 20px;
      box-shadow: 0 4px 12px rgba(255, 255, 255, 0.2);

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 16px rgba(255, 255, 255, 0.3);
      }
    }
  }
}

.word-test-content {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
}

.test-result-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  padding: 20px;
  margin-bottom: 20px;
  position: relative;

  .test-date {
    display: flex;
    align-items: center;
    font-size: 16px;
    font-weight: bold;
    color: #606266;
    margin-bottom: 16px;

    .el-icon {
      margin-right: 8px;
      color: #409eff;
    }
  }

  .test-status-tag {
    width: 60px;
    padding: 4px 12px;
    margin-left: 10px;
    border-radius: 16px;
    font-size: 12px;
    font-weight: bold;
    display: flex;
    align-items: center;

    .el-icon {
      margin-right: 4px;
      font-size: 14px;
    }
  }

  .status-in-progress {
    background-color: #e6a23c;
    color: white;
  }

  .status-completed {
    background-color: #67c23a;
    color: white;
  }
}

.score-overview {
  background: linear-gradient(135deg, #f0f7ff 0%, #e6f3ff 100%);
  border-radius: 16px;
  padding: 20px;
  margin: 20px 0;
  transition: transform 0.3s ease;

  &:hover {
    transform: translateY(-2px);
  }

  .score-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 15px;

    .stat-item {
      text-align: center;
      padding: 15px;
      background: white;
      border-radius: 12px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
      transition: transform 0.3s ease;

      &:hover {
        transform: translateY(-5px);
      }

      .stat-icon {
        font-size: 24px;
        margin-bottom: 8px;
      }

      .stat-value {
        font-size: 22px;
        font-weight: bold;
        color: #303133;
        margin-bottom: 4px;
      }

      .stat-label {
        font-size: 14px;
        color: #909399;
      }
    }
  }
}

.vocabulary-section {
  background: white;
  border-radius: 16px;
  padding: 20px;
  margin: 20px 0;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);

  .level-display {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;

    .level-title {
      display: flex;
      align-items: center;
      font-size: 16px;
      font-weight: bold;
      color: #303133;

      .el-icon {
        margin-right: 8px;
        color: #ffc107;
        font-size: 20px;
      }
    }

    .level-badge {
      padding: 8px 20px;
      border-radius: 20px;
      font-weight: bold;
      font-size: 16px;
      text-transform: uppercase;
      letter-spacing: 1px;
      animation: pulse 2s infinite;

      &.level-low {
        background: linear-gradient(135deg, #ff9a9e 0%, #fad0c4 100%);
        color: #fff;
      }

      &.level-medium {
        background: linear-gradient(135deg, #ffd1ff 0%, #fad0c4 100%);
        color: #fff;
      }

      &.level-high {
        background: linear-gradient(135deg, #84fab0 0%, #8fd3f4 100%);
        color: #fff;
      }
    }
  }

  .vocab-stats {
    .vocab-item {
      display: flex;
      align-items: center;
      padding: 15px;
      background-color: #f5f7fa;
      border-radius: 12px;
      transition: transform 0.3s ease;

      &:hover {
        transform: translateX(5px);
      }

      .vocab-icon {
        font-size: 24px;
        margin-right: 15px;
      }

      .vocab-info {
        flex: 1;

        .vocab-label {
          font-size: 14px;
          color: #606266;
        }

        .vocab-value {
          font-size: 20px;
          font-weight: bold;
          color: #409eff;
          margin-top: 4px;
        }
      }
    }
  }
}

.analysis-section {
  background: white;
  border-radius: 16px;
  padding: 20px;
  margin: 20px 0;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);

  .section-title {
    display: flex;
    align-items: center;
    font-size: 16px;
    font-weight: bold;
    color: #303133;
    margin-bottom: 20px;

    .el-icon {
      margin-right: 8px;
      color: #409eff;
      font-size: 20px;
    }
  }

  .progress-analysis {
    margin: 20px 0;
    
    .progress-item {
      margin-bottom: 15px;
      padding: 15px;
      background: #f8f9fa;
      border-radius: 12px;
      transition: transform 0.3s ease;

      &:hover {
        transform: translateX(5px);
        background: #f0f7ff;
      }

      .progress-label {
        font-size: 14px;
        color: #606266;
        margin-bottom: 10px;
      }

      :deep(.el-progress-bar__outer) {
        border-radius: 8px;
      }

      :deep(.el-progress-bar__inner) {
        border-radius: 8px;
        transition: width 0.6s ease;
      }
    }
  }

  .suggestions-area {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
    margin-top: 20px;

          .suggestion-card {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 15px;
            display: flex;
            gap: 12px;
            transition: all 0.3s ease;
            min-height: 80px;
            overflow: hidden;

            &:hover {
              background: #f0f7ff;
              transform: translateY(-2px);
            }

            &.expanded {
              height: auto;
          
              .suggestion-text {
                display: block;
                white-space: normal;
                -webkit-line-clamp: unset;
              }
            }

            .suggestion-icon {
              font-size: 24px;
              flex-shrink: 0;
            }

            .suggestion-content {
              flex: 1;
              display: flex;
              flex-direction: column;
              overflow: hidden;

              .suggestion-title {
                font-size: 12px;
                color: #909399;
                margin-bottom: 4px;
              }

              .suggestion-text {
                font-size: 14px;
                font-weight: bold;
                color: #303133;
                line-height: 1.5;
            
                &.long-text {
                  display: -webkit-box;
                  -webkit-box-orient: vertical;
                  -webkit-line-clamp: 2;
                  overflow: hidden;
                  text-overflow: ellipsis;
                }
              }

              .expand-toggle {
                color: #409eff;
                font-size: 12px;
                cursor: pointer;
                margin-top: 8px;
                user-select: none;
                transition: color 0.3s ease;

                &:hover {
                  color: #66b1ff;
                }
              }
            }
          }
  }
}

.suggestions-section {
  .suggestions-header {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
    font-weight: bold;
    color: #606266;

    .el-icon {
      margin-right: 8px;
      color: #e6a23c;
    }
  }

  .suggestions-content {
    padding: 12px;
    background-color: #fdf6ec;
    border-radius: 6px;
    color: #e6a23c;
    line-height: 1.5;
  }
}

// 自定义滚动条样式
.word-test-content::-webkit-scrollbar {
  width: 6px;
}

.word-test-content::-webkit-scrollbar-track {
  background: #f5f7fa;
  border-radius: 3px;
}

.word-test-content::-webkit-scrollbar-thumb {
  background: #c0c4cc;
  border-radius: 3px;

  &:hover {
    background: #909399;
  }
}

// 建议区域样式优化
.suggestions-section {
  margin-top: 20px;
  background: white;
  border-radius: 16px;
  padding: 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);

  .suggestions-header {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
    font-size: 16px;
    font-weight: bold;
    color: #303133;

    .el-icon {
      margin-right: 8px;
      color: #409eff;
      font-size: 20px;
    }
  }

  .suggestions-content {
    background-color: #f5f7fa;
    border-radius: 12px;
    overflow: hidden;

    .suggestion-tabs {
      display: flex;
      background: white;
      padding: 4px;
      border-bottom: 1px solid #e4e7ed;

      .tab-item {
        flex: 1;
        text-align: center;
        padding: 12px;
        font-size: 14px;
        color: #606266;
        cursor: pointer;
        transition: all 0.3s ease;
        border-radius: 8px;
        margin: 0 4px;

        &:hover {
          color: #409eff;
          background: #f0f7ff;
        }

        &.active {
          color: #409eff;
          font-weight: bold;
          background: #ecf5ff;
        }
      }
    }

    .tab-content {
      padding: 20px;
      color: #606266;
      font-size: 14px;
      line-height: 1.8;
      position: relative;
      min-height: 100px;
      animation: fadeIn 0.3s ease-out;

      &:not(.plan-content) {
        &::before {
          content: '"';
          position: absolute;
          top: 10px;
          left: 10px;
          font-size: 40px;
          color: #dcdfe6;
          font-family: Arial, sans-serif;
          opacity: 0.5;
        }

        &::after {
          content: '"';
          position: absolute;
          bottom: 0;
          right: 10px;
          font-size: 40px;
          color: #dcdfe6;
          font-family: Arial, sans-serif;
          opacity: 0.5;
        }
      }

      .plan-content {
        .plan-suggestions {
          background: #f5f7fa;
          border-radius: 8px;
          padding: 15px;
          margin-bottom: 20px;
          position: relative;
          
          &::before {
            content: '"';
            position: absolute;
            top: 5px;
            left: 10px;
            font-size: 24px;
            color: #dcdfe6;
            font-family: Arial, sans-serif;
            opacity: 0.5;
          }

          &::after {
            content: '"';
            position: absolute;
            bottom: -5px;
            right: 10px;
            font-size: 24px;
            color: #dcdfe6;
            font-family: Arial, sans-serif;
            opacity: 0.5;
          }
        }

        .plan-cards {
          display: flex;
          flex-direction: column;
          grid-template-columns: repeat(2, 1fr);
          gap: 15px;
          margin-top: 20px;
          animation: slideIn 0.5s ease-out;

          @media (max-width: 768px) {
            grid-template-columns: 1fr;
          }

          .suggestion-card {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 15px;
            display: flex;
            gap: 12px;
            transition: all 0.3s ease;
            min-height: 80px;
            
            &:hover {
              background: #f0f7ff;
              transform: translateY(-2px);
            }

            &.expanded {
              grid-column: span 2;
              
              @media (max-width: 768px) {
                grid-column: span 1;
              }
              
              .suggestion-text {
                display: block;
                white-space: normal;
                -webkit-line-clamp: unset;
              }
            }
          }
        }
      }
    }
  }
}

// 全局动画定义
@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

@keyframes twinkle {
  0% { opacity: 0.5; }
  100% { opacity: 1; }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

// 添加全局过渡效果
.test-result-card {
  animation: slideIn 0.5s ease-out;
}

.stat-item, .vocab-item, .suggestion-card {
  animation: slideIn 0.5s ease-out;
}
</style>