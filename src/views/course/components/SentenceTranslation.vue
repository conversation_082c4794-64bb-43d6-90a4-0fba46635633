<template>
  <div class="stage-container">
    <div class="stage-header">
      <div class="stage-title">3. 【句子测验】（1）听句子发音 （2）尝试自己翻译选择答案</div>
    </div>

    <div class="stage-content">
      <div class="word-display">
        <span class="word-display-text">{{ currentStepInfo?.wordInfo?.sentences.sentenceEn }}</span>
      </div>
      <div class="sentence-video-text">
        <span @click="playAudioUtil(currentStepInfo?.wordInfo?.sentences?.audioUkUrl)" style="margin-right: 20px">英式 <el-icon><VideoPlay /></el-icon></span>
        <span @click="playAudioUtil(currentStepInfo?.wordInfo?.sentences?.audioUsUrl)">美式 <el-icon><VideoPlay /></el-icon></span>
      </div>
      <div class="translation-card">
        <!--        <div class="sentence-display">{{ currentSentence }}</div>-->

        <div class="options-container">
          <div v-for="(option, index) in currentStepInfo?.step?.options" :key="index" class="option-item" :class="{
            'correct': showResult && index == correctAnswer,
            'wrong': showResult && selectedOption == index && index != correctAnswer
          }" @click="selectOption(index)">
            {{ getOptionLabel(index) }}: {{ option }}
            <el-icon v-if="showResult && index == correctAnswer" class="result-icon correct-icon"><Select /></el-icon>
            <el-icon v-if="showResult && selectedOption == index && index != correctAnswer"
                     class="result-icon wrong-icon">
              <Close />
            </el-icon>
          </div>
        </div>
      </div>
    </div>

    <!-- 答对时的星星特效 -->
    <div v-if="showStars" class="stars-container">
      <div v-for="n in 10" :key="n" class="star" :style="getRandomStarStyle()"></div>
    </div>

    <!-- 答对时的奖励徽章 -->
    <div v-if="showReward" class="reward-badge">
      <el-icon class="reward-icon">
        <Trophy />
      </el-icon>
      <span class="reward-text">答对啦</span>
    </div>

  </div>
</template>

<script setup lang="ts">
import {ref} from 'vue'
import {Close, Select, Trophy, VideoPlay} from '@element-plus/icons-vue'
import {
  COURSE_sessionStorage_INFO_KEY,
  CurrentStepInfo,
  getStepInfoByType,
  getWordStepResultByWordId,
  submitCourseStepApi
} from "@/api/course";
import {getOptionIndex, getOptionLabel, getRandomStarStyle, playAudioUtil} from '@/api/course/util'
import {ElMessageBox} from "element-plus";

const emit = defineEmits(['complete'])

const props = defineProps({
  selectWordText: String,
  courseId: {
    type: [String, Number],
    required: true
  }
})
// 状态管理
const selectedOption = ref(-1)
const showResult = ref(false)
const showStars = ref(false)
const showReward = ref(false)

const currentStepInfo = ref<CurrentStepInfo | null>(null);
const correctAnswer = ref(currentStepInfo?.step?.answer) // 当前单词的正确答案
const answerResult = ref(false)

const selectOption = (index: number) => {
  if (showResult.value) return // 如果已经显示结果，不允许再次选择
  selectedOption.value = index

  showResult.value = true

  if (index == correctAnswer.value) {
    showSuccessEffects()
  }
}

const showSuccessEffects = () => {
  showStars.value = true
  showReward.value = true
  setTimeout(() => {
    showStars.value = false
    showReward.value = false
  }, 2000)
}

/**
 * 提交课程步骤
 */
const submitCourseStep = ():boolean => {
  // 如果是已完成状态，不用重复提交
  let status = currentStepInfo.value.step.status
  if (status === "已完成") {
    return true;
  }
  if(selectedOption.value === -1) {
    ElMessageBox.alert("还未选择答案哦，请先选择您的答案吧");
    return false;
  }
  let wordId = currentStepInfo.value.wordId

  let submitResult = {
    stepId: currentStepInfo.value.step.id,
    result: answerResult.value ? "正确" : "错误",
    studentAnswer: getOptionLabel(selectedOption.value)
  }
  submitCourseStepApi(props.courseId, wordId, submitResult, true)
  return true;
}


onMounted(async () => {
  // 先从sessionStorage获取
  let obj = sessionStorage.getItem(COURSE_sessionStorage_INFO_KEY + props.courseId)
  if (obj !== null) {
    try {
      let courseInfo = JSON.parse(obj);
      currentStepInfo.value = await getStepInfoByType(courseInfo, '句子翻译', props.selectWordText)
      if(currentStepInfo.value?.status == '待开始') {
        return ;
      }
      correctAnswer.value = currentStepInfo.value?.step?.answer
      // 获取当前阶段的提交结果
      let submitCourseStepApiParam = getWordStepResultByWordId(props.courseId, currentStepInfo.value.wordId, courseInfo) || {}
      // 如果本地存在提交结果，直接渲染出结果
      submitCourseStepApiParam?.params?.find((item) => {
        if (item.stepId == currentStepInfo.value.step.id && (item.studentAnswer != null && item.studentAnswer != '自动结束')) {
          showResult.value = true
          selectedOption.value = getOptionIndex(item.studentAnswer)  // 学生的回答
        }
      })

      return;
    } catch (e) {
      console.error("Error parsing sessionStorage data:", e);
    }
  }
})

defineExpose({
  submitCourseStep
})
</script>

<style scoped>
.stage-container {
  width: 100%;
  height: 100%;
  flex-direction: column;
  align-items: center;
  padding: 40px;
  overflow-y: hidden; /* 默认不显示滚动条，优先缩小内容 */
}

.stage-header {
  width: 100%;
  margin-bottom: 30px;
}

.stage-title {
  font-size: 24px;
  font-weight: bold;
  color: #5d4037;
}

.translation-card {
  width: 100%;
  padding: 0 20px 20px 20px;
  background: white;
  border-radius: 10px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.word-display {
  font-size: clamp(18px, 3vw, 32px);
  color: #FF9800;
  font-weight: 600;
  text-align: center;
  max-width: 90%;
  margin: 0 auto;
  line-height: 1.4;
}

.sentence-display {
  font-size: clamp(16px, 2.5vw, 24px);
  color: #333;
  margin-bottom: 20px;
  text-align: center;
  line-height: 1.6;
}

.options-container {
  margin-top: 20px;
  width: 100%;
  display: grid;
  grid-template-columns: repeat(1, 1fr);
  gap: 20px;
  color: #5d4037;
}



.option-item {
  padding: clamp(8px, 2vw, 16px);
  background-color: #fff;
  border-radius: 8px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: clamp(14px, 2vw, 24px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
  border: 2px solid #f2ad47;
  transition: all 0.3s ease;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.option-item:hover:not(.correct, .wrong) {
  background-color: #c9e3ff;
  transform: translateY(-2px);
}

/* 正确答案样式 */
.option-item.correct {
  background-color: #c6f6d5;
  border-color: #48bb78;
  color: #276749;
  animation: pulse 0.5s ease;
}

/* 错误答案样式 */
.option-item.wrong {
  background-color: #fed7d7;
  border-color: #f56565;
  color: #c53030;
  animation: shake 0.5s ease;
}

/* 结果图标样式 */
.result-icon {
  font-size: 20px;
}

.result-icon.correct-icon {
  color: #48bb78;
}

.result-icon.wrong-icon {
  color: #f56565;
}

.stage-content {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  min-height: 0;
}

.word-display {
  font-size: clamp(24px, 4vw, 40px);
  font-weight: bold;
  color: #5d4037;
  max-width: 90%;
  margin: 0 auto;
}

.word-display-text {
  animation: bounce 0.5s ease;
  font-family: "Comic Sans MS", cursive, sans-serif;
  margin-right: clamp(20px, 3vw, 40px);
  display: inline-block;
  line-height: 1.4;
  word-wrap: break-word;
}

/* 动画关键帧 */
@keyframes bounce {

  0%,
  100% {
    transform: translateY(0);
  }

  50% {
    transform: translateY(-10px);
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }

  50% {
    transform: scale(1.05);
  }

  100% {
    transform: scale(1);
  }
}

@keyframes shake {

  0%,
  100% {
    transform: translateX(0);
  }

  25% {
    transform: translateX(-5px);
  }

  75% {
    transform: translateX(5px);
  }
}

@keyframes rotate {
  0% {
    transform: rotate(0deg);
  }

  25% {
    transform: rotate(15deg);
  }

  75% {
    transform: rotate(-15deg);
  }

  100% {
    transform: rotate(0deg);
  }
}

/* 星星特效样式 */
.stars-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 100;
}

.star {
  position: absolute;
  width: 20px;
  height: 20px;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23FFD700"><path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z"/></svg>');
  background-repeat: no-repeat;
  background-size: contain;
  opacity: 0;
  animation: starAnimation 2s ease forwards;
}

@keyframes starAnimation {
  0% {
    transform: scale(0) rotate(0deg);
    opacity: 0;
  }

  50% {
    opacity: 1;
  }

  100% {
    transform: scale(1) rotate(360deg);
    opacity: 0;
  }
}

/* 奖励徽章样式 */
.reward-badge {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) scale(0);
  background-color: #ffd700;
  border-radius: 50%;
  width: 80px;
  height: 80px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  box-shadow: 0 0 20px rgba(255, 215, 0, 0.6);
  z-index: 101;
  animation: badgeAnimation 2.5s ease forwards;
  pointer-events: none;
}

.reward-icon {
  font-size: 32px;
  color: #ffffff;
  margin-bottom: 4px;
}

.reward-text {
  font-size: 16px;
  font-weight: bold;
  color: #ffffff;
}

@keyframes badgeAnimation {
  0% {
    transform: translate(-50%, -50%) scale(0);
    opacity: 0;
  }

  50% {
    transform: translate(-50%, -50%) scale(1.2);
    opacity: 1;
  }

  70% {
    transform: translate(-50%, -50%) scale(1);
  }

  100% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 0;
  }
}

.sentence-video-text {
  font-size: 20px;
  margin: 10px 0 15px 0;
  cursor: pointer;
}

/* 响应式设计 - 基于屏幕高度 */
@media screen and (max-height: 700px) {
  .stage-container {
    padding: 20px 15px;
  }
  
  .stage-title {
    font-size: 20px;
    margin-bottom: 20px;
  }
  
  .word-display {
    font-size: 28px;
  }
  
  .sentence-video-text {
    font-size: 18px;
    margin: 8px 0 12px 0;
  }
  
  .options-container {
    gap: 12px;
  }
  
  .option-item {
    padding: 12px 15px;
    font-size: 16px;
  }
  
  .reward-badge {
    width: 70px;
    height: 70px;
  }
  
  .reward-icon {
    font-size: 28px;
  }
  
  .reward-text {
    font-size: 14px;
  }
}

@media screen and (max-height: 500px) {
  .stage-container {
    padding: 10px 8px;
  }
  
  .stage-title {
    font-size: 18px;
    margin-bottom: 10px;
  }
  
  .stage-header {
    margin-bottom: 15px;
  }
  
  .word-display {
    font-size: 24px;
  }
  
  .sentence-video-text {
    font-size: 16px;
    margin: 5px 0 10px 0;
  }
  
  .options-container {
    gap: 8px;
  }
  
  .option-item {
    padding: 10px 12px;
    font-size: 14px;
  }
  
  .reward-badge {
    width: 60px;
    height: 60px;
  }
  
  .reward-icon {
    font-size: 24px;
  }
  
  .reward-text {
    font-size: 12px;
  }
}

/* 极小屏幕高度 - 启用滚动条 */
@media screen and (max-height: 400px) {
  .stage-container {
    padding: 8px 5px;
    overflow-y: auto; /* 在极小屏幕下启用滚动条 */
  }
  
  .stage-title {
    font-size: 16px;
    margin-bottom: 5px;
  }
  
  .stage-header {
    margin-bottom: 8px;
  }
  
  .word-display {
    font-size: 20px;
  }
  
  .sentence-video-text {
    font-size: 14px;
    margin: 3px 0 8px 0;
  }
  
  .options-container {
    gap: 5px;
  }
  
  .option-item {
    padding: 8px 10px;
    font-size: 12px;
  }
  
  .reward-badge {
    width: 50px;
    height: 50px;
  }
  
  .reward-icon {
    font-size: 20px;
    margin-bottom: 2px;
  }
  
  .reward-text {
    font-size: 10px;
  }
}
</style>