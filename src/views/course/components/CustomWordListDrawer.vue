<template>
  <el-drawer
    v-model="visible"
    title="自定义教材"
    direction="rtl"
    size="40%"
    class="custom-word-drawer"
    :before-close="handleClose"
  >
    <div class="drawer-content">
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-position="top"
        @submit.prevent="submitForm"
      >
        <el-form-item label="教材名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入教材名称" />
        </el-form-item>

        <el-form-item prop="wordList">
          <template #label>
            <div style="display: flex; align-items: center">
              <span class="detail-label">单词列表</span>
              <div
                style="display: flex; align-items: center; margin-left: 10px"
              >
                <el-upload
                  class="upload-demo"
                  action="#"
                  ref="uploadRef"
                  :show-file-list="false"
                  :on-change="(uploadFile: UploadFile) => beforeAudioUpload(uploadFile, 'video')"
                  :on-exceed="(files:UploadFile[]) => handleExceed(files, 'video')"
                  :auto-upload="false"
                  limit=1
                  accept=".txt"
                >
                  <el-button type="primary">上传教材txt文件</el-button>
                </el-upload>
                <span style="color: red; margin-left: 10px">{{
                  txtFile?.name
                }}</span>
                <el-icon
                  style="margin-left: 2px; cursor: pointer"
                  @click="removeFile()"
                  v-if="txtFile?.name"
                  ><DeleteFilled
                /></el-icon>
              </div>
            </div>
          </template>
          <el-input
            v-model="form.wordList"
            type="textarea"
            :rows="5"
            placeholder=""
            :disabled="txtFile?.name"
            v-if="txtFile?.name"
          />
        </el-form-item>

        <div class="form-actions">
          <el-button @click="handleClose">取消</el-button>
          <el-button type="primary" native-type="submit" v-loading="loading"> 保存教材 </el-button>
        </div>
      </el-form>
    </div>
  </el-drawer>
</template>

<script setup lang="ts">
import { ref, defineProps, defineEmits, computed } from "vue";
import type { FormInstance, FormRules } from "element-plus";
import { ElMessage, UploadFile } from "element-plus";
import { addOrUpdateTextbook } from "../../../api/textbook";

interface Props {
  visible: boolean;
  studentId: string;
}

const emit = defineEmits<{
  "update:visible": [value: boolean];
}>();
const loading = ref(false);
const uploadRef = ref<UploadFile | null>(null);
const txtFile = ref<UploadFile | null>(null);
const handleExceed = (files, type: string) => {
  txtFile.value = files[0];
};

const removeFile = () => {
  txtFile.value = null;
  form.value.wordList = "";
};

const beforeAudioUpload = (file: UploadFile, type: string) => {
  if (!file.raw) {
    ElMessage.error("文件不存在或读取失败");
    return false;
  }
  txtFile.value = file;

  const reader = new FileReader();
  reader.onload = (e) => {
    const result = e.target?.result;
    if (typeof result === "string") {
      form.value.wordList = result; // 原始内容
      // form.value.words = result.split(/\r?\n/); // 如果你希望是数组的话
    } else {
      console.error("读取失败，文件内容不是字符串");
    }
    // form.value.words = content.split(/\r?\n/) // 按行分割
    // textLinesDisplay.value = textLines.value.join('\n') // 显示在输入框中
  };

  reader.readAsText(file.raw);

  // 阻止默认上传行为
  return false;
};

const props = defineProps<Props>();

const formRef = ref<FormInstance>();
const imageUrl = ref("");
const form = ref({
  name: "",
  wordList: "",
  type: "3",
});

const rules: FormRules = {
  name: [
    { required: true, message: "请输入教材名称", trigger: "blur" },
    { min: 2, max: 50, message: "长度在 2 到 50 个字符", trigger: "blur" },
  ],
  wordList: [
    {
      validator: (rule: any, value: UploadFile[], callback: Function) => {
        if (!value || value.length === 0) {
          callback(new Error("请上传文件"));
        } else {
          callback();
        }
      },
      trigger: "change", // ❗️on-change 触发校验
    },
  ],
};

const handleClose = () => {
  emit("update:visible", false);
};

const submitForm = async (formEl: FormInstance | undefined) => {
  console.log("提交表单");
  if (!formRef.value) return;

  await formRef.value.validate((valid) => {
    if (valid) {
      loading.value = true;
      handleCustomWordListSubmit(form.value);
    }
  });
};

// 处理个性教材提交
const handleCustomWordListSubmit = (formData: any) => {
  console.log("提交的个性教材数据：", formData);
  // 这里可以添加保存个性教材的逻辑
  const formData1 = new FormData();
  formData1.append("name", formData.name);
  formData1.append("wordList", formData.wordList.replaceAll("\r", ""));
  formData1.append("type", formData.type);
  formData1.append("studentId", props.studentId);
  addOrUpdateTextbook(formData1).then(() => {
    ElMessage.success("添加成功");
    emit("update:visible", false);
  }).finally(() => {
    loading.value = false;
  });
};

// 监听visible属性变化
const visible = computed({
  get: () => props.visible,
  set: (value) => {
    emit("update:visible", value);
  },
});
</script>

<style scoped>
.custom-word-drawer {
  --el-drawer-padding-primary: 20px;
  --el-drawer-bg-color: #fff8e1;
}

.header-title {
  font-size: 20px;
  font-weight: bold;
  color: #ff9800;
  margin-bottom: 20px;
  padding: 0 10px;
}

.custom-word-form {
  padding: 0 10px;
}

.el-form-item {
  margin-bottom: 20px;
}

.el-form-item__label {
  color: #ff9800;
  font-weight: bold;
}

.cover-uploader {
  border: 2px dashed #ffd54f;
  border-radius: 10px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: all 0.3s;
  background: white;
}

.cover-uploader:hover {
  border-color: #ff9800;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(255, 152, 0, 0.15);
}

.cover-uploader-icon {
  font-size: 28px;
  color: #ffd54f;
  width: 178px;
  height: 178px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.cover-image {
  width: 100%;
  height: 100%;
  display: block;
  object-fit: cover;
}

.form-actions {
  margin-top: 30px;
  display: flex;
  justify-content: flex-end;
  gap: 15px;
}

.el-button {
  transition: all 0.3s;
}

.el-button--primary {
  background: linear-gradient(135deg, #ff9800, #ffc107);
  border: none;
}

.el-button--primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(255, 152, 0, 0.3);
}

.el-input__inner,
.el-textarea__inner {
  border-radius: 8px;
  border: 1px solid #ffd54f;
}

.el-input__inner:focus,
.el-textarea__inner:focus {
  border-color: #ff9800;
  box-shadow: 0 0 0 2px rgba(255, 152, 0, 0.2);
}
</style>
