<template>
  <div class="word-test-container">
    <div class="test-header">
      <div class="test-title">1.【单词测验】大胆凭感觉选择！错了也不要紧！我们后面会一起学习！</div>
    </div>
    <div class="test-content">
      <div class="word-display">{{ currentStepInfo?.wordInfo?.word }}</div>
      <div class="options-container">
        <div v-for="(option, index) in currentStepInfo?.step?.options" :key="index" class="option-item" :class="{
          'correct': showResult && index == correctAnswer,
          'wrong': showResult && selectedOption == index && index != correctAnswer
        }" @click="selectOption(index)" :disabled="showResult">
          {{ getOptionLabel(index) }}. {{ option }}
          <el-icon v-if="showResult && index == correctAnswer" class="result-icon correct-icon"><Select /></el-icon>
          <el-icon v-if="showResult && selectedOption == index && index != correctAnswer" class="result-icon wrong-icon">
            <Close />
          </el-icon>
        </div>
      </div>
      <div class="unknown-word-button" @click="markAsUnknown" :class="{ 'disabled': showResult, 'unknown-word-selected': unknownWord }">
        🤷‍♂️ 这个词我还不认识
      </div>
    </div>

    <!-- 答对时的星星特效 -->
    <div v-if="showStars" class="stars-container">
      <div v-for="n in 10" :key="n" class="star" :style="getRandomStarStyle()"></div>
    </div>

    <!-- 答对时的奖励徽章 -->
    <div v-if="showReward" class="reward-badge">
      <el-icon class="reward-icon">
        <Trophy />
      </el-icon>
      <span class="reward-text">答对啦</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import {defineProps, onMounted, ref} from 'vue'
import {Close, Select, Trophy} from '@element-plus/icons-vue'
import {
  COURSE_sessionStorage_INFO_KEY,
  CurrentStepInfo,
  getStepInfoByType,
  getWordStepResultByWordId,
  submitCourseStepApi
} from "@/api/course";
import {getOptionIndex, getOptionLabel, getRandomStarStyle} from '@/api/course/util'
import {ElMessage, ElMessageBox} from "element-plus";

const props = defineProps({
  selectWordText: String,
  courseId: {
    type: [String, Number],
    required: true
  }
})
const currentStepInfo = ref<CurrentStepInfo | null>(null);
// 基础数据
const correctAnswer = ref(currentStepInfo?.wordInfo?.step?.answer) // 当前单词的正确答案
// 状态管理
const selectedOption = ref(-1)
const showResult = ref(false)
const showStars = ref(false)
const showReward = ref(false)
const answerResult = ref(false)
const unknownWord = ref(false)

const showSuccessEffects = () => {
  showStars.value = true
  showReward.value = true
  setTimeout(() => {
    showStars.value = false
    showReward.value = false
  }, 2000)
}

const selectOption = (option: number) => {
  if (showResult.value) return // 如果已经显示结果，不允许再次选择
  selectedOption.value = option
  showResult.value = true

  if (option == correctAnswer.value) {
    answerResult.value = true
    showSuccessEffects()
  }
}


/**
 * 提交课程步骤
 */
const markAsUnknown = () => {
  if (showResult.value) return // 如果已经显示结果，不允许再次操作
  
  showResult.value = true
  selectedOption.value = -2 // 使用-2表示"不认识"
  answerResult.value = false

  let wordId = currentStepInfo.value.wordId
  let submitResult = {
    stepId: currentStepInfo.value.step.id,
    result: "错误",
    studentAnswer: "不会"
  }
  //debugger
  submitCourseStepApi(props.courseId, wordId, submitResult, true)
  // ElMessageBox.alert("已标记为不认识，下次再来吧");
  ElMessage.success('已标记为不认识，下次再来吧')
}

const submitCourseStep = ():boolean => {
  ////debugger
  // 如果是已完成状态，不用重复提交
  let status = currentStepInfo.value.step.status
  if (status === "已完成") {
    return true;
  }
  if(selectedOption.value === -1) {
    ElMessageBox.alert("还未选择答案哦，请先选择您的答案吧");
    return false;
  }
  //debugger
  if(selectedOption.value === -2) { // 表示这个词不认识了
    return true;
  }
  let wordId = currentStepInfo.value.wordId

  let submitResult = {
    stepId: currentStepInfo.value.step.id,
    result: answerResult.value ? "正确" : "错误",
    studentAnswer: getOptionLabel(selectedOption.value)
  }
  submitCourseStepApi(props.courseId, wordId, submitResult, true)
  return true;
}

onMounted(async () => {
  // 先从sessionStorage获取
  let obj = sessionStorage.getItem(COURSE_sessionStorage_INFO_KEY + props.courseId)
  if (obj !== null) {
    try {
      let courseInfo = JSON.parse(obj);
      currentStepInfo.value = await getStepInfoByType(courseInfo, '单词测试', props.selectWordText)
      correctAnswer.value = currentStepInfo.value.step.answer
      if(currentStepInfo.value?.status == '待开始') {
        return ;
      }
      // 获取当前阶段的提交结果
      let submitCourseStepApiParam = getWordStepResultByWordId(props.courseId, currentStepInfo.value.wordId, courseInfo) || {}
      // 如果本地存在提交结果，直接渲染出结果
      submitCourseStepApiParam?.params?.find((item) => {
        if (item.stepId == currentStepInfo.value.step.id && (item.studentAnswer != null && item.studentAnswer != '自动结束')) {
          showResult.value = true
          //debugger
          if(item.studentAnswer === '不会'){
            selectedOption.value = -2
            unknownWord.value = true
          } else {
            selectedOption.value = getOptionIndex(item.studentAnswer)  // 学生的回答
          }
        }
      })

      return;
    } catch (e) {
      console.error("Error parsing sessionStorage data:", e);
    }
  }
})

defineExpose({
  submitCourseStep
})
</script>

<style scoped>
.word-test-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40px;
  /* 默认不显示滚动条，只在内容溢出时才显示 */
  overflow-y: hidden;
}

.test-header {
  width: 100%;
  margin-bottom: 10px;
}

.test-title {
  font-size: 24px;
  font-weight: bold;
  color: #5d4037;
}

.test-content {
  width: 100%;
  max-width: 600px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.word-display {
  font-size: 70px;
  font-weight: bold;
  color: #5d4037;
  margin-bottom: 40px;
  animation: bounce 0.5s ease;
  font-family: "Comic Sans MS", cursive, sans-serif;
}

.options-container {
  width: 100%;
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
  color: #5d4037;
  margin-bottom: 20px; /* 添加底部边距，确保与下方按钮有足够间隔 */
}

.option-item {
  padding: 20px;
  background-color: #fff;
  border-radius: 8px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 18px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
  border: 2px solid #f2ad47;
  transition: all 0.3s ease;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.option-item:hover:not(.correct, .wrong) {
  background-color: #c9e3ff;
  transform: translateY(-2px);
}

/* 正确答案样式 */
.option-item.correct {
  background-color: #c6f6d5;
  border-color: #48bb78;
  color: #276749;
  animation: pulse 0.5s ease;
}

/* 错误答案样式 */
.option-item.wrong {
  background-color: #fed7d7;
  border-color: #f56565;
  color: #c53030;
  animation: shake 0.5s ease;
}

/* 结果图标样式 */
.result-icon {
  font-size: 20px;
}

.result-icon.correct-icon {
  color: #48bb78;
}

.result-icon.wrong-icon {
  color: #f56565;
}

/* 动画关键帧 */
@keyframes bounce {

  0%,
  100% {
    transform: translateY(0);
  }

  50% {
    transform: translateY(-10px);
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }

  50% {
    transform: scale(1.05);
  }

  100% {
    transform: scale(1);
  }
}

@keyframes shake {

  0%,
  100% {
    transform: translateX(0);
  }

  25% {
    transform: translateX(-5px);
  }

  75% {
    transform: translateX(5px);
  }
}

@keyframes rotate {
  0% {
    transform: rotate(0deg);
  }

  25% {
    transform: rotate(15deg);
  }

  75% {
    transform: rotate(-15deg);
  }

  100% {
    transform: rotate(0deg);
  }
}

/* 星星特效样式 */
.stars-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 100;
}

.star {
  position: absolute;
  width: 20px;
  height: 20px;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23FFD700"><path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z"/></svg>');
  background-repeat: no-repeat;
  background-size: contain;
  opacity: 0;
  animation: starAnimation 2s ease forwards;
}

@keyframes starAnimation {
  0% {
    transform: scale(0) rotate(0deg);
    opacity: 0;
  }

  50% {
    opacity: 1;
  }

  100% {
    transform: scale(1) rotate(360deg);
    opacity: 0;
  }
}

/* 奖励徽章样式 */
.reward-badge {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) scale(0);
  background-color: #ffd700;
  border-radius: 50%;
  width: 80px;
  height: 80px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  box-shadow: 0 0 20px rgba(255, 215, 0, 0.6);
  z-index: 101;
  animation: badgeAnimation 2.5s ease forwards;
  pointer-events: none;
}

.reward-icon {
  font-size: 32px;
  color: #ffffff;
  margin-bottom: 4px;
}

.reward-text {
  font-size: 16px;
  font-weight: bold;
  color: #ffffff;
}

@keyframes badgeAnimation {
  0% {
    transform: translate(-50%, -50%) scale(0);
    opacity: 0;
  }

  50% {
    transform: translate(-50%, -50%) scale(1.2);
    opacity: 1;
  }

  70% {
    transform: translate(-50%, -50%) scale(1);
  }

  100% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 0;
  }
}

/* 不认识按钮样式 */
.unknown-word-button {
  margin-top: 20px; /* 改为固定像素值，避免使用vh单位导致在小屏幕上间距过大 */
  padding: 12px 20px;
  width: 80%;
  max-width: 400px;
  min-height: 48px;
  color: #5d4037;
  cursor: pointer;
  font-size: clamp(14px, 2vw, 16px);
  transition: all 0.3s ease;
  text-align: center;
  border: 2px dashed #f2ad47;
  border-radius: 8px;
  background-color: transparent;
  display: flex;
  align-items: center;
  justify-content: center;
}

.unknown-word-button:hover:not(.disabled) {
  color: #f2ad47;
  background-color: rgba(242, 173, 71, 0.1);
}

.unknown-word-button.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  border-color: #ccc;
  color: #999;
}
.unknown-word-selected {
  background-color: #fbb1b1  !important;
  color: #000000 !important;
  border: 2px solid red !important;
}

/* 响应式设计 - 屏幕宽度 */
@media screen and (max-width: 768px) {
  .word-test-container {
    padding: 20px 15px;
  }
  
  .test-title {
    font-size: 20px;
  }
  
  .word-display {
    font-size: 50px;
    margin-bottom: 30px;
  }
  
  .options-container {
    gap: 15px;
    margin-bottom: 15px;
  }
  
  .option-item {
    padding: 15px;
    font-size: 16px;
  }
  
  .result-icon {
    font-size: 18px;
  }
  
  .unknown-word-button {
    margin-top: 15px;
    padding: 10px 15px;
  }
}

@media screen and (max-width: 480px) {
  .word-test-container {
    padding: 15px 10px;
  }
  
  .test-title {
    font-size: 18px;
  }
  
  .word-display {
    font-size: 40px;
    margin-bottom: 20px;
  }
  
  .options-container {
    grid-template-columns: 1fr;
    gap: 12px;
    margin-bottom: 12px;
  }
  
  .option-item {
    padding: 12px;
    font-size: 14px;
  }
  
  .result-icon {
    font-size: 16px;
  }
  
  .unknown-word-button {
    margin-top: 12px;
    padding: 8px 12px;
    width: 90%;
    font-size: 14px;
    min-height: 40px;
  }
  
  .reward-badge {
    width: 60px;
    height: 60px;
  }
  
  .reward-icon {
    font-size: 24px;
  }
  
  .reward-text {
    font-size: 12px;
  }
}

/* 响应式设计 - 屏幕高度 */
@media screen and (max-height: 700px) {
  .word-test-container {
    padding: 15px 10px;
  }
  
  .test-title {
    font-size: 18px;
    margin-bottom: 5px;
  }
  
  .word-display {
    font-size: 40px;
    margin-bottom: 15px;
  }
  
  .options-container {
    gap: 10px;
    margin-bottom: 10px;
  }
  
  .option-item {
    padding: 10px;
    font-size: 14px;
  }
  
  .unknown-word-button {
    margin-top: 10px;
    padding: 8px 12px;
    min-height: 36px;
    width: 90%;
    max-width: 380px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

@media screen and (max-height: 500px) {
  .word-test-container {
    padding: 10px 8px;
  }
  
  .test-header {
    margin-bottom: 5px;
  }
  
  .test-title {
    font-size: 16px;
  }
  
  .word-display {
    font-size: 32px;
    margin-bottom: 10px;
  }
  
  .options-container {
    gap: 8px;
    margin-bottom: 8px;
  }
  
  .option-item {
    padding: 8px;
    font-size: 12px;
  }
  
  .unknown-word-button {
    margin-top: 8px;
    padding: 6px 10px;
    min-height: 30px;
    font-size: 12px;
    width: 90%;
    max-width: 300px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .reward-badge {
    width: 50px;
    height: 50px;
  }
  
  .reward-icon {
    font-size: 20px;
  }
  
  .reward-text {
    font-size: 10px;
  }
}

/* 极小屏幕高度 - 启用滚动条 */
@media screen and (max-height: 400px) {
  .word-test-container {
    padding: 8px 5px;
    overflow-y: auto; /* 在极小屏幕下启用滚动条 */
  }
  
  .test-title {
    font-size: 14px;
  }
  
  .word-display {
    font-size: 28px;
    margin-bottom: 8px;
  }
  
  .options-container {
    gap: 6px;
    margin-bottom: 6px;
  }
  
  .option-item {
    padding: 6px;
    font-size: 11px;
  }
  
  .unknown-word-button {
    margin-top: 8px;
    padding: 6px 10px;
    min-height: 30px;
    font-size: 12px;
    width: 90%;
    max-width: 300px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
</style>