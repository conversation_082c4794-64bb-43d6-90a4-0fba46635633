<template>
  <div class="stage-container">
    <div class="stage-header">
      <div class="stage-title">5. 欢迎来到筱灵老师的超级单词讲解！<br>请先认真听课记笔记，然后和你的老师一起搞定后面的挑战噢！</div>
    </div>

    <div class="stage-content">
      <!--      <div class="word-display">-->
      <!--        <span class="word-display-text">{{ currentStepInfo?.wordInfo?.word }}</span>-->
      <!--      </div>-->
      <div class="video-card">
        <video v-if="currentStepInfo?.wordInfo?.videoUrl" :src="currentStepInfo?.wordInfo?.videoUrl" controls
          style="max-width: 40%; max-height: 70vh"></video>
        <div class="video-wrapper" v-else>
          <div class="video-placeholder">
            <el-icon :size="50">
              <Close />
            </el-icon>
            <p style="text-align: center">暂无视频</p>
          </div>
        </div>

        <!--        <div class="video-info">-->
        <!--          <h4 >单词"<b class="video-title">{{ currentStepInfo?.wordInfo?.word }}</b>"的用法讲解</h4>-->
        <!--          <div class="video-description">本视频详细讲解单词"<b>{{ currentStepInfo?.wordInfo?.word }}</b>"的各种用法、搭配和常见错误。</div>-->
        <!--        </div>-->
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import {ref} from 'vue'
import {Close} from '@element-plus/icons-vue'
import {COURSE_sessionStorage_INFO_KEY, CurrentStepInfo, getStepInfoByType, submitCourseStepApi} from "@/api/course";

const props = defineProps({
  selectWordText: String,
  courseId: {
    type: [String, Number],
    required: true
  }
})

const emit = defineEmits(['complete'])

const currentStepInfo = ref<CurrentStepInfo | null>(null);

/**
 * 提交课程步骤
 */
const submitCourseStep = () => {
  // 如果是已完成状态，不用重复提交
  let status = currentStepInfo.value.step.status
  if (status === "已完成") {
    return;
  }
  let wordId = currentStepInfo.value.wordId

  let submitResult = {
    stepId: currentStepInfo.value.step.id,
    result: "正确",
    studentAnswer: "正确"
  }
  submitCourseStepApi(props.courseId, wordId, submitResult, true)
}

onMounted(async () => {
  // 先从sessionStorage获取
  let obj = sessionStorage.getItem(COURSE_sessionStorage_INFO_KEY + props.courseId)
  if (obj !== null) {
    try {
      let courseInfo = JSON.parse(obj);
      currentStepInfo.value = await getStepInfoByType(courseInfo, '视频讲解', props.selectWordText)
      return;
    } catch (e) {
      console.error("Error parsing sessionStorage data:", e);
    }
  }
})

defineExpose({
  submitCourseStep
})
</script>

<style scoped>
.stage-container {
  width: 100%;
  height: 100%;
  flex-direction: column;
  align-items: center;
  padding: 40px;
}

.stage-header {
  width: 100%;
  margin-bottom: 20px;
}

.stage-title {
  font-size: 24px;
  font-weight: bold;
  color: #5d4037;
}

.video-card {
  width: 100%;
  padding: 20px;
  background: white;
  border-radius: 10px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stage-content {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  min-height: 0;
}

.video-wrapper {
  width: 40%;
  position: relative;
  /* 16:9 比例 */
  padding-bottom: 22.5%;
  background: #000;
  border-radius: 8px;
  overflow: hidden;
}

.video-placeholder {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: white;
  cursor: pointer;
}

.video-placeholder .el-icon {
  color: #FF9800;
  margin-bottom: 10px;
}
</style>