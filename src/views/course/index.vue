<template>
  <div
    class="course-page-container"
    style="
      width: calc(100% - 40px);
      border: 4px #ffd54f solid;
      border-radius: 20px;
      height: calc(100vh - 50px);
      margin: 20px;
      background-color: #fff8e1;
      box-shadow: 0 12px 24px rgba(0, 0, 0, 0.15);
    "
  >
    <!-- 装饰元素 -->
    <div class="decoration-star star1"></div>
    <div class="decoration-star star2"></div>
    <div class="decoration-star star3"></div>
    <div class="decoration-circle circle1"></div>
    <!-- <div class="decoration-circle circle2"></div> -->
    <div class="course-container">
      <div class="course-content-wrapper">
        <div class="course-content">
          <template v-if="!isLearning && !showWordTestResult">
            <div class="course-main-area">
              <div class="welcome-header">
                <span class="welcome-text"
                  >欢迎来到北大军哥名师团的单词乐园！</span
                >
              </div>
              <div class="course-mascot">
                <img
                  src="@/assets/icons/svg/education.svg"
                  alt="小老师"
                  class="bouncing-image"
                />
              </div>
              <div class="course-prompt">
                <div class="speech-bubble">
                  <span class="wave-text"
                    >嗨！准备好开始我们的<br />单词挑战了吗？</span
                  >
                  <div class="prompt-action">点击下方"开始上课"按钮吧！</div>
                </div>
              </div>
              <div class="learning-tips">
                <div class="tip-item">
                  <div class="tip-icon">💡</div>
                  <div class="tip-text">每天学习一点点，进步看得见！</div>
                </div>
                <div class="tip-item">
                  <div class="tip-icon">🎮</div>
                  <div class="tip-text">学习也可以很有趣！</div>
                </div>
                <div class="tip-item">
                  <div class="tip-icon">🏆</div>
                  <div class="tip-text">坚持就是胜利！</div>
                </div>
              </div>
            </div>
          </template>

          <!-- 阶段内容 -->
          <div :class="showWordTestResult?'learning-stage-container-100':'learning-stage-container'">
            <!-- 阶段1、单词测验 -->
            <WordQuiz
              ref="WordQuizRef"
              v-if="
                isLearning &&
                currentSectionRef?.type !== '词汇测验' &&
                currentStageType === '单词测试'
              "
              @progress="updateProgress"
              :selectWordText="currentWord"
              :key="currentWord"
              :courseId="courseId"
              @complete="canProceedToNext = true"
            />

            <VocabularyTest
              ref="VocabularyTestRef"
              v-else-if="
                (isLearning &&
                currentSectionRef?.type === '词汇测验' &&
                currentStageType === '单词测试') || showWordTestResult
              "
              @progress="updateProgress"
              :selectWordText="currentWord"
              :key="currentWord"
              :showWordTestResult="showWordTestResult"
              :courseId="courseId"
              @complete="canProceedToNext = true"
              @timerOverCall="timerOverCall"
            />

            <WordListenerQuiz
              ref="WordListenerQuizRef"
              v-else-if="isLearning && currentStageType === '单词测试2'"
              @progress="updateProgress"
              :selectWordText="currentWord"
              :key="currentWord"
              :courseId="courseId"
              @complete="canProceedToNext = true"
            />

            <!-- 阶段2、单词释义 -->
            <WordDefinition
              ref="WordDefinitionRef"
              v-else-if="isLearning && currentStageType === '单词讲解'"
              @progress="updateProgress"
              :selectWordText="currentWord"
              :key="currentWord"
              :courseId="courseId"
              @complete="canProceedToNext = true"
            />

            <!-- 单词填空测验 -->
            <WordFillBlank
              ref="WordFillBlankRef"
              v-else-if="isLearning && currentStageType === '单词听写'"
              @progress="updateProgress"
              :selectWordText="currentWord"
              :key="currentWord"
              :courseId="courseId"
              @complete="canProceedToNext = true"
            />

            <!-- 阶段3、句子翻译测验 -->
            <SentenceTranslation
              ref="SentenceTranslationRef"
              v-else-if="isLearning && currentStageType === '句子翻译'"
              :selectWordText="currentWord"
              :key="currentWord"
              @progress="updateProgress"
              :courseId="courseId"
              @complete="canProceedToNext = true"
            />

            <!-- 阶段4、句子释义 -->
            <SentenceDefinition
              ref="SentenceDefinitionRef"
              v-else-if="isLearning && currentStageType === '句子翻译讲解'"
              @progress="updateProgress"
              :selectWordText="currentWord"
              :key="currentWord"
              :courseId="courseId"
              @complete="canProceedToNext = true"
            />

            <!-- 阶段5、讲解视频 -->
            <VideoExplanation
              ref="VideoExplanationRef"
              v-else-if="isLearning && currentStageType === '视频讲解'"
              @progress="updateProgress"
              :selectWordText="currentWord"
              :key="currentWord"
              :courseId="courseId"
              @complete="canProceedToNext = true"
            />

            <!-- 阶段6、句子排序测验 -->
            <SentenceArrangement
              ref="SentenceArrangementRef"
              v-else-if="isLearning && currentStageType === '句子排序'"
              :selectWordText="currentWord"
              :key="currentWord"
              @progress="updateProgress"
              :courseId="courseId"
              @complete="canProceedToNext = true"
            />

            <!-- 阶段6、句子填空 -->
            <SentenceFillBlank
              ref="SentenceFillBlankRef"
              v-else-if="isLearning && currentStageType === '句子填空'"
              :selectWordText="currentWord"
              :key="currentWord"
              @progress="updateProgress"
              :courseId="courseId"
              @complete="canProceedToNext = true"
            />

            <!-- 导航按钮 -->
            <div class="stage-navigation" v-show="isLearning === true">
              <el-button
                type="primary"
                class="nav-button home-button"
                @click="goToHome"
              >
                <el-icon>
                  <el-icon><HomeFilled /></el-icon>
                </el-icon>
                <span>主页</span>
              </el-button>
              <el-button
                type="primary"
                class="nav-button prev-button"
                @click="goToPrevStage"
                v-show="currentStage !== 0"
              >
                <el-icon>
                  <ArrowLeft />
                </el-icon>
                <span>上一步</span>
              </el-button>

              <div class="stage-dots">
                <div
                  v-for="(n, i) in steps.length || 6"
                  :key="i"
                  class="stage-dot"
                  :class="{ active: i === currentStage }"
                ></div>
              </div>

              <el-button
                type="primary"
                class="nav-button next-button"
                v-if="currentStage !== (steps.length || 6) - 1"
                @click="goToNextStage"
              >
                <span>下一步</span>
                <el-icon>
                  <ArrowRight />
                </el-icon>
              </el-button>

              <el-button
                type="primary"
                class="nav-button next-button"
                v-else-if="
                  currentStage === (steps.length || 6) - 1 &&
                  lastWord !== currentWord
                "
                @click="goToNextStage"
              >
                <span>下一个单词</span>
                <el-icon>
                  <ArrowRight />
                </el-icon>
              </el-button>

              <template
                v-else-if="
                  currentStage === (steps.length || 6) - 1 &&
                  lastWord === currentWord
                "
              >
                <slot name="complete-button">
                  <el-button
                    type="primary"
                    class="nav-button next-button"
                    @click="goToNextStage"
                  >
                    <span
                      >完成{{
                        currentSectionRef?.type === "词汇测验" ? "测验" : "学习"
                      }}</span
                    >
                    <el-icon>
                      <ArrowRight />
                    </el-icon>
                  </el-button>
                </slot>
              </template>
            </div>
          </div>
        </div>
        <!-- 底部按钮区 -->
        <div class="course-actions" v-if="courseInfo?.status == '待开始'">
          <el-button
            type="primary"
            class="action-button right-action"
            :disabled="courseInfo?.status == '已完成'"
            @click="startCourse"
          >
            <i class="el-icon-video-play action-icon"></i>
            开始上课
          </el-button>
        </div>
        <div class="course-actions" v-else-if="courseInfo?.status == '进行中'">
          <el-button
            type="primary"
            class="action-button left-action"
            :disabled="courseInfo?.status == '已完成'"
            @click="quickReview"
            :key="currentSectionRef?.status"
            v-if="
              currentSectionRef == null || currentSectionRef?.status == '已完成'
            "
          >
            <i class="el-icon-refresh-right action-icon"></i>
            抗遗忘复习
          </el-button>
          <el-button
            type="primary"
            class="action-button left-action"
            :disabled="courseInfo?.status == '已完成'"
            @click="startEndReview"
            :key="currentSectionRef?.status"
            v-if="
              courseInfo?.type === '学习课' &&
              (currentSectionRef == null ||
                currentSectionRef?.status == '已完成')
            "
          >
            <i class="el-icon-refresh-right action-icon"></i>
            下课前复习
          </el-button>
          <el-button
            type="primary"
            class="action-button right-action"
            :disabled="courseInfo?.status == '已完成'"
            v-if="
              courseInfo?.type === '学习课' &&
              (currentSectionRef == null ||
                currentSectionRef?.status == '已完成')
            "
            @click="openCourseMaterials"
          >
            <i class="el-icon-video-play action-icon"></i>
            新课学习
          </el-button>
          <el-button
            type="primary"
            class="action-button right-action"
            :disabled="courseInfo?.status == '已完成'"
            v-if="
              currentSectionRef?.status == '进行中' ||
              currentSectionRef?.status == '已完成'
            "
            @click="endCourse"
          >
            <i class="el-icon-video-play action-icon"></i>
            下课
          </el-button>
        </div>

        <div class="tips-actions" v-if="showTips">
          <el-icon><Flag /></el-icon
          >今天还没有进行下课前复习，但是后面的5次抗遗忘一定要好好复习噢！
        </div>
      </div>

      <!-- 单词列表区 -->
      <div class="word-list-container" v-if="isLearning">
        <div class="word-list-header">
          <div>单词列表</div>
          <div class="word-count">{{ wordList.length }}个</div>
        </div>
        <div class="word-list custom-scrollbar">
          <div
            v-for="(word, index) in wordList"
            :key="index"
            class="word-item"
            @click.stop="selectWordItem(word.id, word.word)"
            :class="{ 'word-actived': word.word == currentWord }"
          >
            <div class="word-content">
              <span
                class="word-text"
                v-if="completeWords.indexOf(word.word) === -1"
                >{{ getDisplayWord(word.word) }}</span
              >
              <span class="word-text" v-else>{{ word.word }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧按钮区，复习篮子 正课资料 -->
      <div class="course-sidebar" :class="{ minimized: isLearning || showWordTestResult }">
        <el-button
          :icon="EditPen"
          type="primary"
          class="sidebar-button"
          v-if="true"
          :disabled="
            courseInfo?.status == '已完成' ||
            currentSectionRef?.status == '进行中'
          "
          @click="openTestBasket"
          >单词测验</el-button
        >
        <el-button
          :icon="MessageBox"
          type="primary"
          class="sidebar-button"
          :disabled="
            courseInfo?.status == '已完成' ||
            currentSectionRef?.status == '进行中'
          "
          @click="openReviewBasket"
          >复习篮子</el-button
        >
        <el-button
          :icon="Operation"
          type="primary"
          class="sidebar-button"
          :disabled="
            courseInfo?.status == '已完成' ||
            currentSectionRef?.status == '进行中'
          "
          @click="openCourseMaterials"
          >正课资料</el-button
        >
        <el-button
          :icon="Plus"
          type="primary"
          class="sidebar-button"
          :disabled="courseInfo?.status == '已完成'"
          @click="addCustomWordList"
          >个性教材</el-button
        >
        <el-button
          :icon="Notebook"
          type="primary"
          class="sidebar-button"
          @click="openSectionList"
          >学习环节</el-button
        >
        <el-dropdown
          class="sidebar-dropdown"
          :disabled="courseInfo?.status == '已完成'"
          :hide-timeout="300"
        >
          <el-button
            class="sidebar-button"
            :disabled="courseInfo?.status == '已完成'"
            :icon="TrophyBase"
            type="primary"
          >
            积分奖励
            <el-icon class="el-icon--right"><arrow-down /></el-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item @click="award('进步之星', 5)"
                >进步之星 + 5</el-dropdown-item
              >
              <el-dropdown-item @click="award('明日之星', 5)"
                >明日之星 + 5</el-dropdown-item
              >
              <el-dropdown-item @click="award('最佳答案', 10)"
                >最佳答案 + 10</el-dropdown-item
              >
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
      <!-- 正课资料抽屉组件 -->
      <div v-if="testBasketVisible">
        <WordTestDrawer
          v-model:visible="testBasketVisible"
          :courseId="courseId"
          :studentId="courseRequiredInfo.studentId"
          @startLearning="startLearning"
        />
      </div>
      <!-- 正课资料抽屉组件 -->
      <div v-if="courseMaterialsVisible">
        <CourseMaterialsDrawer
          v-model:visible="courseMaterialsVisible"
          :courseId="courseId"
          :studentId="courseRequiredInfo.studentId"
          @startLearning="startLearning"
        />
      </div>

      <div v-if="reviewBasketVisible">
        <ReviewBasketDrawer
          v-model:visible="reviewBasketVisible"
          :studentId="courseRequiredInfo.studentId"
          :courseId="courseId"
          @startReviewCourse="startLearning"
        />
      </div>
      <!-- 个性教材抽屉组件 -->
      <div v-if="customWordListVisible">
        <CustomWordListDrawer
          v-model:visible="customWordListVisible"
          :studentId="courseRequiredInfo.studentId"
          :courseId="courseId"
        />
      </div>
      <!-- 复习篮子抽屉组件 -->

      <!-- 环节列表 -->
      <div v-if="sectionListVisible">
        <SectionListDrawer
          v-model:visible="sectionListVisible"
          :studentId="courseRequiredInfo.studentId"
          :courseId="courseId"
          @changeCurrentStageType="changeCurrentStageType"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, nextTick, onMounted, ref, watch } from "vue";
import { useRouter } from "vue-router";
import CourseMaterialsDrawer from "./components/CourseMaterialsDrawer.vue";
import CustomWordListDrawer from "./components/CustomWordListDrawer.vue";
import ReviewBasketDrawer from "./components/ReviewBasketDrawer.vue";
import SectionListDrawer from "./components/SectionListDrawer.vue";
import WordQuiz from "./components/WordQuiz.vue";
import WordFillBlank from "./components/WordFillBlank.vue";
import WordDefinition from "./components/WordDefinition.vue";
import SentenceTranslation from "./components/SentenceTranslation.vue";
import SentenceDefinition from "./components/SentenceDefinition.vue";
import VideoExplanation from "./components/VideoExplanation.vue";
import SentenceArrangement from "./components/SentenceArrangement.vue";
import SentenceFillBlank from "@/views/course/components/SentenceFillBlank.vue";
import VocabularyTest from "@/views/course/components/VocabularyTest.vue";
import {
  ArrowDown,
  ArrowLeft,
  ArrowRight,
  EditPen,
  Flag,
  HomeFilled,
  MessageBox,
  Notebook,
  Operation,
  Plus,
  TrophyBase,
} from "@element-plus/icons-vue";
import {
  COURSE_sessionStorage_INFO_KEY,
  CourseInfo,
  CourseRequiredInfo,
  endCourseApi,
  fetchCourseInfoApi,
  getCurrentWordInfo,
  getWordInfoByWord,
  getWordStepResultByWordId,
  Section,
  startCourseApi,
  startEndReviewApi,
} from "@/api/course";
import { loading } from "@/api/course/util";
import { awardApi } from "@/api/course/award";
import { ElMessage, ElMessageBox } from "element-plus";
import WordListenerQuiz from "@/views/course/components/WordListenerQuiz.vue";
import WordTestDrawer from "./components/WordTestDrawer.vue";

// 添加复习模式状态管理
const isReviewMode = ref(false);

const sectionType = ref<string>();

/**
 * 创建单词遮挡函数
 * @param word 原单词
 * @returns 用*号替换的单词
 */
const maskWord = (word: string): string => {
  if (!word) return "";
  return "*".repeat(word.length);
};

/**
 * 根据复习模式状态决定显示原单词还是遮挡单词
 * @param word 原单词
 * @returns 显示的单词
 */
const getDisplayWord = (word: string): string => {
  return isReviewMode.value ? maskWord(word) : word;
};

interface Word {
  id: string;
  word: string;
  syllables: string;
  phoneticUk?: string;
  phoneticUs?: string;
  difficulty?: string;
  marked: boolean;
}
//debugger
//
const courseInfo = ref<CourseInfo | null>(null);
const currentSectionRef = ref<Section | null>(null);
const WordQuizRef = ref<any>();
const VocabularyTestRef = ref<any>();
const WordListenerQuizRef = ref<any>();
const WordDefinitionRef = ref<any>();
const WordFillBlankRef = ref<any>();
const SentenceTranslationRef = ref<any>();
const SentenceDefinitionRef = ref<any>();
const VideoExplanationRef = ref<any>();
const SentenceArrangementRef = ref<any>();
const SentenceFillBlankRef = ref<any>();
const currentWord = ref<string | null>(null);
const currentLearningSectionIndex = ref<number>(0);
const currentSelectSectionIndex = ref<number>(0);
const lastWord = ref<string | null>(null);
const completeWords = ref<string[]>([]);
const courseId = ref<string>("");
// courseId.value = courseIdParam
//debugger
const { currentRoute } = useRouter();
const showTips = computed(() => {
  // 如果课程是已完成的状态下，判断当前是否进行过下课前复习
  if (courseInfo.value?.status === "已完成") {
    // 遍历section，如果都没包含下课前复习，那么就显示该提示showTips
    let sections = courseInfo.value?.content?.sections;
    let shwTipsFlag = sections.some((section) => section?.type === "下课复习");
    return !shwTipsFlag;
  }
  return false;
});

onMounted(() => {
  const courseIdParam = currentRoute.value.query.courseId as string;
  if (!courseIdParam) {
    ElMessage.error("课程ID不能为空");
    return;
  }

  courseId.value = courseIdParam;
});

const courseRequiredInfo = ref<CourseRequiredInfo>({
  courseId: null,
  studentId: null,
  teacherId: null,
});
courseRequiredInfo.value.courseId = courseId.value;

/**
 * 开始上课后的回调接口
 * @param sectionInfo 当前环节信息
 */
const startLearning = (sectionInfo: Section) => {
  sectionType.value = sectionInfo.type;
  fetchCourseInfo();
};

const fetchCourseInfo = async () => {
  try {
    let res = await fetchCourseInfoApi(courseId.value);
    console.log('showWordTestResult变化',showWordTestResult.value);
    if (res.code === 200) {
      courseInfo.value = res.data; // 赋值整个对象
      sessionStorage.setItem(
        COURSE_sessionStorage_INFO_KEY + courseId.value,
        JSON.stringify(courseInfo.value)
      );
      await courseInfoInit();
    }
  } catch (error) {
    console.error("获取课程信息失败:", error);
  } finally {
    showWordTestResult.value = false;
  };
};

/**
 * 组装课程信息逻辑
 * @param courseInfo
 */
const courseInfoInit = async () => {
  await nextTick();
  // 提取必须数据，如courseId，studentId, teacherId
  courseRequiredInfo.value.teacherId = courseInfo.value?.teacher?.id;
  courseRequiredInfo.value.studentId = courseInfo.value?.student?.id;
  if (
    courseRequiredInfo.value.teacherId == null ||
    courseRequiredInfo.value.studentId == null
  ) {
    ElMessage.error("获取课程信息失败，请重新进入课程页面或联系管理员");
    return;
  }
  // 如果content为空，则需要渲染未上课页面
  if (
    courseInfo.value?.content?.sections == null ||
    courseInfo.value.content.sections.length == 0
  ) {
    isLearning.value = false;
    return;
  }

  // 如果不为空，获取每个环节的数据
  let currentSectionIndex = courseInfo.value.content.currentSectionIndex;
  currentLearningSectionIndex.value = currentSectionIndex;
  // 重新加载页面，那么当作当前环节被选中
  currentSelectSectionIndex.value = currentSectionIndex;
  // 这里获取对应的section数据
  let currentSection = courseInfo.value.content.sections[currentSectionIndex];
  currentSectionRef.value = currentSection;

  isReviewMode.value =
    currentSection.type !== "新课程学习" && currentSection.type !== "词汇测验"; // 如果当前环节是复习环节，则设置复习模式为true

  // 当前环节，学习到了哪个单词
  let currentWordIndex = currentSection?.currentWordIndex;
  // 获取当前的单词数据
  let currentWordInfo = currentSection?.words?.[currentWordIndex];
  currentWord.value = getCurrentWordInfo(courseInfo.value)?.word;
  // 获取当前单词学习到了哪个阶段
  let currentStepIndex = currentWordInfo?.currentStepIndex;
  // 当前单词所有的步骤
  steps.value = currentWordInfo?.steps?.map((step) => step.type) || [];

  currentStage.value = currentStepIndex;
  // 获取每个阶段的数据
  let currentStep = currentWordInfo?.steps?.[currentStepIndex];
  isLearning.value = currentSectionRef.value?.status !== "已完成";
  if (currentSectionRef.value?.status !== "已完成") {
    currentStageType.value = currentStep?.type;
  }
  // 提取单词列表
  wordList.value =
    currentSection.words?.map((word) => ({
      id: word.id,
      word: word.wordInfo?.word,
      syllables: word.wordInfo?.syllables,
      phoneticUk: word.wordInfo?.phoneticUk,
      phoneticUs: word.wordInfo?.phoneticUs,
      difficulty: word.wordInfo?.difficulty,
      marked: false,
    })) || [];
  // 获取单词列表的最后一个单词
  lastWord.value = wordList.value?.slice(-1)[0]?.word;
  // 判断单词列表中已完成学习的单词
  completeWords.value = [];
  currentSection.words?.forEach((word) => {
    if (word?.steps?.every((s) => s.status === "已完成")) {
      if (!completeWords.value.includes(word?.wordInfo?.word)) {
        completeWords.value?.push(word?.wordInfo?.word);
      }
    }
  });
};

// 控制正课资料抽屉的显示
const courseMaterialsVisible = ref(false);
// 控制个性教材抽屉的显示
const customWordListVisible = ref(false);
// 控制复习篮子抽屉的显示
const reviewBasketVisible = ref(false);
//单词测验
const testBasketVisible = ref(false);
// 环节列表visible
const sectionListVisible = ref(false);
// 控制学习状态
const isLearning = ref(true);
// 控制当前学习阶段
const currentStage = ref(0); // 序号：0: 单词测验, 1: 单词释义, 2: 句子翻译测验, 3: 句子释义, 4: 讲解视频, 5: 句子排序测验
const currentStageType = ref<String>(); //序号对应类型： 0: 单词测验, 1: 单词释义, 2: 句子翻译测验, 3: 句子释义, 4: 讲解视频, 5: 句子排序测验
const steps = ref<String[]>([]);
// 单词列表数据
const wordList = ref<Word[]>([]);
const canProceedToNext = ref(true);
const stageProgress = ref(0);
const progressText = ref("准备开始");
const progressColor = ref("#FF9800");
// 开始上课按钮disabled
const startCourseBtnDisabled = ref(false);
const endCourseBtnDisabled = ref(false);

const updateProgress = (progress: number, text: string) => {
  stageProgress.value = Math.min(100, Math.max(0, progress));
  progressText.value = text || "进行中";
  progressColor.value =
    progress >= 80 ? "#4CAF50" : progress >= 50 ? "#FFC107" : "#FF9800";
};

/**
 * 回到首页
 */
const goToHome = () => {
  // window.location.reload()
  fetchCourseInfo();
};

// 导航方法
const goToPrevStage = () => {
  if (currentStage.value > 0) {
    currentStage.value--;
    let preStage = steps.value[currentStage.value] || null;
    if (preStage != null) {
      currentStageType.value = preStage;
      return;
    }
  }
};

const goToNextStage = () => {
  // debugger
  let selectSectionFlag =
    currentLearningSectionIndex.value === currentSelectSectionIndex.value &&
    currentSectionRef.value?.status !== "已完成";
  if (currentStageType.value === "单词测试" && selectSectionFlag) {
    if (currentSectionRef.value?.type == "词汇测验") {
      let flag = VocabularyTestRef.value.submitCourseStep();
      if (!flag) return;
    } else {
      let flag = WordQuizRef.value.submitCourseStep();
      if (!flag) return;
    }
  }
  if (currentStageType.value === "单词测试2" && selectSectionFlag) {
    let flag = WordListenerQuizRef.value.submitCourseStep();
    if (!flag) return;
  }
  if (currentStageType.value === "单词讲解" && selectSectionFlag) {
    WordDefinitionRef.value.submitCourseStep();
  }
  if (currentStageType.value === "单词听写" && selectSectionFlag) {
    let flag = WordFillBlankRef.value.submitCourseStep();
    if (!flag) return;
  }
  if (currentStageType.value === "句子翻译" && selectSectionFlag) {
    let flag = SentenceTranslationRef.value.submitCourseStep();
    if (!flag) return;
  }
  if (currentStageType.value === "句子翻译讲解" && selectSectionFlag) {
    SentenceDefinitionRef.value.submitCourseStep();
  }
  if (currentStageType.value === "视频讲解" && selectSectionFlag) {
    VideoExplanationRef.value.submitCourseStep();
  }
  if (currentStageType.value === "句子排序" && selectSectionFlag) {
    let flag = SentenceArrangementRef.value.submitCourseStep();
    if (!flag) return;
  }
  if (currentStageType.value === "句子填空" && selectSectionFlag) {
    let flag = SentenceFillBlankRef.value.submitCourseStep();
    if (!flag) return;
  }
  if (currentStageType.value === "句子填空" && selectSectionFlag) {
    let flag = SentenceFillBlankRef.value.submitCourseStep();
    if (!flag) return;
  }
  // 获取下一个阶段
  const currentIndex = steps.value.findIndex(
    (item) => item === currentStageType.value
  );
  let nextStage = steps.value[currentIndex + 1] || null;
  if (nextStage != null) {
    currentStageType.value = nextStage;
    currentStage.value++;
    return;
  }
  if (!completeWords.value.includes(currentWord.value)) {
    completeWords.value.push(currentWord.value);
  }
  // 阶段结束了，下一个单词
  goToNextWord();
};

const showWordTestResult = ref(false);

/**
 * 跳转到下一个单词
 */
const goToNextWord = async () => {
  // 边界检查：如果单词列表为空，直接返回
  if (wordList.value.length === 0) {
    ElMessage.info("单词列表为空");
    return;
  }

  // 如果没有当前选中的单词，默认选择第一个单词
  if (!currentWord.value) {
    currentWord.value = wordList.value[0].word;
    return;
  }
  const currentIndex = wordList.value.findIndex(
    (item) => item.word === currentWord.value
  );
  if (currentIndex === -1) {
    ElMessage.info(`未找到单词: ${currentWord.value}`);
    return;
  }

  // 如果是最后一个单词
  if (currentIndex === wordList.value.length - 1) {
    if (currentSectionRef.value?.type == "词汇测验") {
      showWordTestResult.value = true;
    }
    await fetchCourseInfo();
    if (currentSectionRef.value?.type == "词汇测验") {
      showWordTestResult.value = true;
    }
    console.log('showWordTestResult变化111',showWordTestResult.value);
    console.log("已经是最后一个单词");
    // 这里可以触发完成学习的回调或提示
    //词汇测验
    if (currentSectionRef.value?.type === "词汇测验") {
      ElMessage.success("恭喜你，完成了词汇测验~");
    } else {
      ElMessage.success("恭喜你，完成所有的单词学习~");
    }

    isLearning.value = false;
    //debugger
    currentSectionRef.value!.status = "已完成";
    return;
  }

  // 切换到下一个单词
  let wordObj = wordList.value[currentIndex + 1];
  currentWord.value = wordObj.word;
  selectWordItem(wordObj.id, wordObj.word);
};
// 下课
const endCourse = () => {
  endCourseBtnDisabled.value = true;
  endCourseApi(courseId.value).finally(async () => {
    ElMessage.success("恭喜你，完成所有的单词学习，可以下课啦");
    endCourseBtnDisabled.value = false;
    isLearning.value = false;
    await fetchCourseInfo();
  });
};
// 开始上课
const startCourse = () => {
  // isLearning.value = true
  // startCourseBtnDisabled.value = true
  // currentStage.value = 0 // 默认从单词测验阶段开始

  // 调用api接口，照例说这里应该要是同步
  startCourseApi(courseId.value).finally(() => {
    startCourseBtnDisabled.value = false;
    courseMaterialsVisible.value = true;
    //debugger
    courseInfo.value.status = "进行中";
  });
};

// 抗遗忘复习
const quickReview = () => {
  isReviewMode.value = true; // 设置复习模式状态
  reviewBasketVisible.value = true;
};

// 打开复习篮子
const openReviewBasket = () => {
  reviewBasketVisible.value = true;
};

const openTestBasket = () => {
  testBasketVisible.value = true;
};

// 打开正课资料
const openCourseMaterials = () => {
  console.log("打开正课资料");
  courseMaterialsVisible.value = true;
};

// 添加个性教材
const addCustomWordList = () => {
  customWordListVisible.value = true;
};

/**
 * 打开学习环节列表
 */
const openSectionList = () => {
  sectionListVisible.value = true;
};

/**
 * 下课前复习
 */
const startEndReview = () => {
  isReviewMode.value = true; // 设置复习模式状态
  const $loading = loading("请稍后...");
  startEndReviewApi(courseId.value)
    .then((res) => {
      fetchCourseInfo();
    })
    .catch((err) => {
      ElMessageBox.alert(err, "提示", {
        confirmButtonText: "好的",
      });
    })
    .finally(() => $loading?.close());
};

/**
 * 点击切换单词
 * @param id
 * @param wordText
 */
const selectWordItem = async (id: string, wordText: string) => {
  // 如果点击的是已完成的单词，允许直接切换并从第一个step开始查看
  if (completeWords.value.includes(wordText)) {
    currentWord.value = wordText;
    let word = await getWordInfoByWord(courseInfo.value, wordText);
    const steps = word?.steps;

    // 直接设置为第一个step
    currentStage.value = 0;
    currentStageType.value = steps[0].type;
    isLearning.value = true;
    return;
  }

  // 获取当前点击的单词在列表中的索引
  const clickedIndex = wordList.value.findIndex(
    (item) => item.word === wordText
  );

  // 检查是否是第一个单词或前面的单词是否都已完成
  const isFirstWord = clickedIndex === 0;
  const previousWordsCompleted =
    clickedIndex > 0 &&
    wordList.value
      .slice(0, clickedIndex)
      .every((word) => completeWords.value.includes(word.word));

  // 如果不是第一个单词，且前面的单词没有全部完成，显示提示并阻止切换
  if (
    !isFirstWord &&
    !previousWordsCompleted &&
    currentLearningSectionIndex.value === currentSelectSectionIndex.value &&
    currentSectionRef.value?.status !== "已完成"
  ) {
    ElMessage.warning("请按顺序学习单词，完成前面的单词后才能学习后面的单词");
    return;
  }

  // 如果当前单词未完成且尝试切换到其他单词，阻止切换
  if (
    currentWord.value &&
    !completeWords.value.includes(currentWord.value) &&
    currentWord.value !== wordText &&
    currentLearningSectionIndex.value === currentSelectSectionIndex.value &&
    currentSectionRef.value?.status !== "已完成"
  ) {
    ElMessage.warning("请先完成当前单词的学习");
    return;
  }

  // 通过所有检查后，执行切换逻辑
  currentWord.value = wordText;
  let submitCourseStepResult = getWordStepResultByWordId(courseId.value, id);
  const targetIdValues = submitCourseStepResult?.params?.map(
    (target) => target.stepId
  );
  let word = await getWordInfoByWord(courseInfo.value, wordText);
  const steps = word?.steps;
  currentStage.value = 0;
  currentStageType.value = steps[0].type;
  isLearning.value = true;

  if (steps) {
    for (let i = steps.length - 1; i >= 0; i--) {
      if (targetIdValues?.includes(steps[i].id)) {
        currentStage.value = i === steps.length - 1 ? steps.length - 1 : i;
        currentStageType.value = steps[i].type;
        break;
      }
    }
  }
};

// 监听复习篮子抽屉的关闭状态
watch(reviewBasketVisible, (newVal) => {
  if (!newVal) {
    isReviewMode.value = false; // 当抽屉关闭时，重置复习模式状态
  }
});

const award = (type: string, point: number) => {
  awardApi(courseId.value, { type: type, points: point }).catch((err) => {
    ElMessageBox.alert(err, "提示", {
      // if you want to disable its autofocus
      // autofocus: false,
      confirmButtonText: "好的",
    });
  });
};

const changeCurrentStageType = (
  sectionIndex: number,
  type: string,
  wordText: string
) => {
  //debugger
  currentSelectSectionIndex.value = sectionIndex;
  currentStage.value = 0;
  currentStageType.value = type;
  currentWord.value = wordText;
  isLearning.value = true;

  let currentSection = courseInfo.value.content.sections[sectionIndex];
  // 提取单词列表
  wordList.value =
    currentSection.words?.map((word) => ({
      id: word.id,
      word: word.wordInfo?.word,
      syllables: word.wordInfo?.syllables,
      phoneticUk: word.wordInfo?.phoneticUk,
      phoneticUs: word.wordInfo?.phoneticUs,
      difficulty: word.wordInfo?.difficulty,
      marked: false,
    })) || [];
  // 获取单词列表的最后一个单词
  lastWord.value = wordList.value?.slice(-1)[0]?.word;
  // 判断单词列表中已完成学习的单词
  completeWords.value = [];
  currentSection.words?.forEach((word) => {
    if (word?.steps?.every((s) => s.status === "已完成")) {
      if (!completeWords.value.includes(word?.wordInfo?.word)) {
        completeWords.value?.push(word?.wordInfo?.word);
      }
    }
  });
};

const timerOverCall = () => {
  goToNextStage();
};

onMounted(async () => {
  // 先从sessionStorage获取
  // let obj = sessionStorage.getItem(COURSE_sessionStorage_INFO_KEY + courseId);
  // if (obj !== null) {
  //   try {
  //     courseInfo.value = JSON.parse(obj);
  //     await courseInfoInit();
  //     return;
  //   } catch (e) {
  //     console.error("Error parsing sessionStorage data:", e);
  //   }
  // }
  fetchCourseInfo();
});
</script>

<style lang="scss" scoped>
/* 装饰元素样式 */
.course-page-container {
  position: relative;
  overflow: hidden;
}

.decoration-star {
  position: absolute;
  background: #ffd54f;
  clip-path: polygon(
    50% 0%,
    61% 35%,
    98% 35%,
    68% 57%,
    79% 91%,
    50% 70%,
    21% 91%,
    32% 57%,
    2% 35%,
    39% 35%
  );
  z-index: 1;
}

.star1 {
  width: 60px;
  height: 60px;
  top: 30px;
  left: 40px;
  animation: twinkle 4s ease-in-out infinite;
}

.star2 {
  width: 40px;
  height: 40px;
  bottom: 50px;
  right: 80px;
  animation: twinkle 5s ease-in-out infinite 1s;
}

.star3 {
  width: 30px;
  height: 30px;
  top: 70px;
  right: 120px;
  animation: twinkle 3s ease-in-out infinite 0.5s;
}

.decoration-circle {
  position: absolute;
  border-radius: 50%;
  z-index: 1;
}

.circle1 {
  width: 100px;
  height: 100px;
  bottom: 40px;
  left: 60px;
  background: radial-gradient(
    circle,
    rgba(255, 193, 7, 0.2) 0%,
    rgba(255, 193, 7, 0) 70%
  );
  animation: float 8s ease-in-out infinite;
}

.circle2 {
  width: 150px;
  height: 150px;
  top: 100px;
  right: 60px;
  background: radial-gradient(
    circle,
    rgba(76, 175, 80, 0.2) 0%,
    rgba(76, 175, 80, 0) 70%
  );
  animation: float 10s ease-in-out infinite 2s;
}

@keyframes twinkle {
  0%,
  100% {
    opacity: 0.4;
    transform: scale(0.8);
  }

  50% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0) translateX(0);
  }

  25% {
    transform: translateY(-15px) translateX(15px);
  }

  50% {
    transform: translateY(0) translateX(30px);
  }

  75% {
    transform: translateY(15px) translateX(15px);
  }
}

/* 按钮样式增强 */
.action-icon {
  margin-right: 8px;
  font-size: 20px;
}

.el-dropdown-menu {
  border-radius: 15px !important;
  padding: 10px !important;
  background: #fff8e1 !important;
  border: 2px solid #ffd54f !important;
}

.el-dropdown-menu__item {
  font-size: 16px !important;
  padding: 12px 20px !important;
  border-radius: 10px !important;
  margin: 5px 0 !important;
  color: #ff9800 !important;
  font-weight: bold !important;
}

.el-dropdown-menu__item:hover {
  background: #ffe0b2 !important;
  color: #f57c00 !important;
}

/* 滚动条美化 */
.word-list {
  max-height: calc(100vh - 300px);
  /* 设置最大高度，启用滚动 */
  overflow-y: auto;
  /* 启用垂直滚动 */
  scrollbar-width: thin;
  scrollbar-color: #ffb74d #fff3e0;
}

.word-list::-webkit-scrollbar {
  width: 8px;
}

.word-list::-webkit-scrollbar-track {
  background: #fff3e0;
  border-radius: 10px;
}

.word-list::-webkit-scrollbar-thumb {
  background: #ffb74d;
  border-radius: 10px;
  border: 2px solid #fff3e0;
}

.word-list::-webkit-scrollbar-thumb:hover {
  background: #ff9800;
}

/* 学习状态样式增强 */
.learning-stage {
  background: #ffffff !important;
  border-radius: 20px !important;
  padding: 20px !important;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1) !important;
  margin: 20px !important;
  border: 3px solid #ffd54f !important;
}

.learning-stage-header {
  font-size: 24px !important;
  color: #ff9800 !important;
  margin-bottom: 15px !important;
  font-weight: bold !important;
  text-align: center !important;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1) !important;
}

/* 欢迎区域样式 */
.welcome-header {
  text-align: center;
  margin-bottom: 30px;
  position: relative;
}

.welcome-text {
  font-size: 36px;
  font-weight: bold;
  color: #ff9800;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
  background: linear-gradient(45deg, #ff9800, #ffc107);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: welcomeScale 2s ease-in-out infinite;
}

@keyframes welcomeScale {
  0%,
  100% {
    transform: scale(1);
  }

  50% {
    transform: scale(1.05);
  }
}

/* 学习提示样式 */
.learning-tips {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-top: 40px;
  flex-wrap: wrap;
  padding: 0 20px;
}

.tip-item {
  background: #ffffff;
  border-radius: 15px;
  padding: 15px 20px;
  display: flex;
  align-items: center;
  gap: 10px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  border: 2px solid #ffd54f;
  transition: all 0.3s ease;
  cursor: pointer;
  min-width: 200px;
}

.tip-item:hover {
  transform: translateY(-5px) rotate(2deg);
  box-shadow: 0 8px 20px rgba(255, 152, 0, 0.2);
  background: #fff8e1;
}

.tip-icon {
  font-size: 24px;
  animation: bounce 2s infinite;
}

.tip-text {
  font-size: 16px;
  color: #ff9800;
  font-weight: bold;
}

/* 增强现有动画效果 */
.bouncing-image {
  animation: bounce 2s ease infinite, glow 3s ease-in-out infinite;
}

@keyframes glow {
  0%,
  100% {
    filter: drop-shadow(0 0 5px rgba(255, 152, 0, 0.5));
  }

  50% {
    filter: drop-shadow(0 0 15px rgba(255, 152, 0, 0.8));
  }
}

.speech-bubble {
  animation: float 6s ease-in-out infinite;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .course-container {
    padding: 20px;
  }

  .course-actions {
    flex-direction: column;
    gap: 15px;
  }

  .word-list-container {
    width: 100%;
    margin-top: 20px;
  }
}

.course-container {
  display: flex;
  justify-content: flex-start;
  align-items: flex-start;
  height: 100%;
  width: 100%;
  padding: 40px 80px 20px 40px;
  position: relative;
  gap: 20px;
}

.course-content-wrapper {
  flex: 1;
  display: flex;
  flex-direction: column;
  position: relative;
  min-width: 300px;
  height: 100%;
}

.course-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: calc(100vh - 100px);
  border: 4px solid #ffb74d;
  border-radius: 20px;
  overflow: hidden;
  position: relative;
  background: linear-gradient(135deg, #ffe0b2, #ffecb3);
  box-shadow: 0 8px 24px rgba(255, 167, 38, 0.2);
  min-width: 300px;
}

.word-list-container {
  width: 150px;
  height: calc(100vh - 240px);
  /* 设置固定高度 */
  background: #ffffff;
  border-radius: 16px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  display: flex;
  flex-direction: column;
  border: 3px solid #ffd54f;
  animation: float 6s ease-in-out infinite;
  overflow: hidden;
  /* 确保内容不会溢出 */
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0);
  }

  50% {
    transform: translateY(-10px);
  }
}

.word-list-header {
  padding: 15px;
  border-bottom: 2px dashed #ffb74d;
  display: flex;
  //justify-content: space-between;
  flex-wrap: wrap;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #fff8e1, #ffecb3);
  border-radius: 13px 13px 0 0;
  font-weight: bold;
  color: #f57c00;
  font-size: 18px;
  flex-shrink: 0;
  /* 防止header被压缩 */
}

.word-count {
  color: #ff9800;
  font-size: 16px;
  background: #fff3e0;
  padding: 4px 8px;
  border-radius: 4px;
  border: 1px solid #ffd54f;
  margin-top: 5px;
  width: 76px;
}

.word-list {
  flex: 1;
  overflow-y: auto;
  padding: 10px;
  background: #fff8e1;
  scrollbar-width: thin;
  /* Firefox */
  scrollbar-color: #ffb74d #fff3e0;
  /* Firefox */

  /* 自定义滚动条样式 */
  &::-webkit-scrollbar {
    width: 8px;
    border-radius: 4px;
  }

  &::-webkit-scrollbar-track {
    background: #fff3e0;
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb {
    background: #ffb74d;
    border-radius: 4px;
    border: 2px solid #fff3e0;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: #ff9800;
  }
}

.word-item {
  padding: 10px;
  border-radius: 6px;
  background: #ffffff;
  margin-bottom: 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
  border: 2px solid #ffe0b2;
  transition: all 0.3s ease;
  cursor: pointer;
}

.word-content {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.word-text {
  font-size: 16px;
  color: #ff9800;
  font-weight: 600;
  letter-spacing: 0.5px;
}

.word-phonetic {
  font-size: 14px;
  color: #9c27b0;
  font-style: italic;
}

.word-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 16px rgba(255, 152, 0, 0.2);
  border-color: #ffb74d;
  background: #ecf5ff;
}

.word-actions {
  display: flex;
  gap: 4px;
}

.word-actions .el-button {
  padding: 6px;
}

.word-actived {
  background: #ffdbb3;
}

.course-main-area {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  flex: 1;
  padding: 40px 0;
}

.course-mascot {
  width: 160px;
  height: 160px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 20px;
  position: relative;
}

.bouncing-image {
  width: 100%;
  height: 100%;
  animation: bounce 2s ease infinite;
}

@keyframes bounce {
  0%,
  100% {
    transform: translateY(0);
  }

  50% {
    transform: translateY(-20px);
  }
}

.speech-bubble {
  position: relative;
  background: #ffffff;
  border-radius: 20px;
  padding: 20px 30px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  max-width: 400px;

  &:after {
    content: "";
    position: absolute;
    top: -20px;
    left: 50%;
    transform: translateX(-50%);
    border: 10px solid transparent;
    border-bottom-color: #ffffff;
  }
}

.wave-text {
  font-size: 28px;
  color: #ff9800;
  font-weight: bold;
  display: block;
  margin-bottom: 10px;
  animation: wave 2s ease-in-out infinite;
}

.prompt-action {
  font-size: 20px;
  color: #4caf50;
  margin-top: 10px;
}

@keyframes wave {
  0%,
  100% {
    transform: rotate(-3deg);
  }

  50% {
    transform: rotate(3deg);
  }
}

.course-actions {
  display: flex;
  justify-content: center;
  gap: 40px;
  margin: 20px auto;
  width: 100%;
  max-width: 600px;
  position: relative;
  z-index: 10;
  padding: 0 20px;
}

.action-button {
  min-width: 160px !important;
  height: 50px !important;
  border-radius: 25px !important;
  font-size: 18px !important;
  font-weight: bold !important;
  transition: all 0.3s ease !important;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1) !important;
  position: relative;
  overflow: hidden;
}

.action-button::before {
  content: "";
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: rgba(255, 255, 255, 0.2);
  transform: rotate(45deg);
  animation: shine 3s infinite;
}

@keyframes shine {
  0% {
    transform: translateX(-100%) rotate(45deg);
  }

  100% {
    transform: translateX(100%) rotate(45deg);
  }
}

.left-action {
  background: linear-gradient(135deg, #ff9800, #ffc107) !important;
  border: none !important;
}

.right-action {
  background: linear-gradient(135deg, #4caf50, #8bc34a) !important;
  border: none !important;
}

.action-button:hover {
  transform: translateY(-5px) scale(1.05) !important;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15) !important;
}

.course-sidebar {
  position: fixed;
  top: 50%;
  transform: translateY(-50%);
  right: 0;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 15px;
  padding: 20px;
  border-radius: 20px 0 0 20px;
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  box-shadow: -4px 0 20px rgba(0, 0, 0, 0.1);
  cursor: pointer;
}

.course-sidebar.minimized {
  transform: translate(calc(100% - 70px), -50%);
}

.course-sidebar.minimized:hover {
  transform: translateY(-50%);
}

.sidebar-icons {
  display: flex;
  flex-direction: column;
  gap: 15px;
  align-items: flex-end;
}

.sidebar-icons .el-button {
  width: 50px !important;
  height: 50px !important;
  padding: 0 !important;
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
  border-radius: 15px !important;
  background: linear-gradient(135deg, #ff9800, #ffc107) !important;
  border: none !important;
  transition: all 0.3s ease !important;
}

.sidebar-button {
  width: 120px !important;
  height: 45px !important;
  border-radius: 15px !important;
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
  font-size: 16px !important;
  font-weight: bold !important;
  background: linear-gradient(135deg, #ff9800, #ffc107) !important;
  border: none !important;
  color: white !important;
  transition: all 0.3s ease !important;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1) !important;
}

.sidebar-button:hover {
  transform: scale(1.1) rotate(3deg) !important;
  box-shadow: 0 8px 25px rgba(255, 152, 0, 0.3) !important;
}

.sidebar-icons .el-button:hover {
  transform: scale(1.1) rotate(3deg) !important;
  box-shadow: 0 8px 25px rgba(255, 152, 0, 0.3) !important;
}

.el-dropdown {
  margin-top: 10px;
}

.el-dropdown .el-button {
  background: linear-gradient(135deg, #ff9800, #ffc107) !important;
  border: none !important;
  border-radius: 15px !important;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1) !important;
}

.stage-navigation {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 45px;
  padding: clamp(8px, 2vw, 15px) clamp(60px, 8vw, 120px);
  border-radius: 0 0 20px 20px;
  margin-top: auto;
  width: 100%;
  box-shadow: 0 -4px 12px rgba(0, 0, 0, 0.05);
}

.stage-dots {
  display: flex;
  gap: clamp(8px, 1vw, 12px);
  justify-content: center;
  align-items: center;
  margin: 0 auto;
  flex-wrap: wrap;
  max-width: 80%;
  padding: 5px 0;
}

.stage-dot {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background: #e5c28e;
  transition: all 0.3s ease;
}

.stage-dot.active {
  width: 14px;
  height: 14px;
  background: #ff9800;
  transform: scale(1.2);
}

.nav-button {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  min-width: clamp(100px, 15vw, 120px);
  height: clamp(32px, 4vh, 36px);
  border-radius: 20px;
  font-size: clamp(12px, 1.5vw, 14px);
  font-weight: bold;
  transition: all 0.3s ease;
  z-index: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 clamp(10px, 2vw, 20px);
  white-space: nowrap;
}

.nav-button:hover {
  transform: translateY(-50%) scale(1.05) !important;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.home-button {
  left: clamp(10px, 2vw, 20px);
  background: linear-gradient(135deg, #ff183b, #f34163);
  border: none;
}

.prev-button {
  left: clamp(148px, 15vw, 20px);
  background: linear-gradient(135deg, #ff9800, #ffc107);
  border: none;
}

.next-button {
  right: clamp(10px, 2vw, 20px);
  background: linear-gradient(135deg, #4caf50, #8bc34a);
  border: none;
}

@media (max-width: 480px) {
  .nav-button {
    padding: 0 8px;
    min-width: auto;
  }

  .nav-button span {
    display: none;
  }

  .nav-button .el-icon {
    margin: 0;
  }
}

.nav-button .el-icon {
  margin: 0 5px;
}
.learning-stage-container {
  height: calc(100% - 60px);
}
.learning-stage-container-100{
  height: 100%;
}
.sidebar-button[disabled] {
  background: #c3c3c3 !important;
}
.action-button[disabled] {
  background: #c3c3c3 !important;
}
.sidebar-dropdown[disabled] {
  background: #c3c3c3 !important;
}
.el-dropdown .el-button[disabled] {
  background: #c3c3c3 !important;
}
.tips-actions {
  display: flex;
  justify-content: center;
  gap: 10px;
  margin: 20px auto;
  width: 100%;
  max-width: 600px;
  position: relative;
  z-index: 10;
  padding: 0 20px;
  align-items: center;
  color: rgb(0 162 24);
  font-weight: bold;
}
</style>
