# 课程模块说明

## 单词测验结果展示页面 (WordTestDrawer.vue)

### 2024年更新记录

#### 2024-01 布局优化更新
1. 页面结构调整
   - 将"建议教材"和"建议学习时间"移至学习计划模块
   - 优化了学习计划标签页的内容布局
   - 改进了长文本内容的展示方式

2. 样式优化
   - 为学习计划模块添加新的卡片布局
   - 优化引用样式，区分普通建议和学习计划
   - 增强了视觉层次感
   - 添加响应式布局支持

3. 交互优化
   - 保留了建议教材的展开/收起功能
   - 优化了卡片hover效果
   - 添加了新的动画效果

4. 响应式设计
   - 在移动端视图下自动调整为单列布局
   - 优化了展开状态下的卡片布局
   - 确保在各种屏幕尺寸下的良好显示效果

#### 技术实现
```scss
// 学习计划卡片布局
.plan-cards {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15px;
  
  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
}

// 展开状态处理
.expanded {
  grid-column: span 2;
  
  @media (max-width: 768px) {
    grid-column: span 1;
  }
}
```

#### 注意事项
1. 学习计划模块现包含三个主要部分：
   - 特色词汇学习计划说明
   - 建议教材信息
   - 建议学习时间
2. 响应式布局断点设置在 768px
3. 保持了原有的空值处理逻辑
4. 建议教材展开状态仍不会持久化

### 早期更新记录

#### 2023年更新记录

##### 2023-12 功能更新
1. 添加了特色词汇学习计划展示
   - 新增 `specialWordLearnSuggestions` 字段的展示
   - 在建议区域添加"学习计划"标签页
   - 空内容显示"暂无"提示

2. 优化长文本内容展示
   - 为建议教材添加展开/收起功能
   - 长文本默认显示两行，超出部分显示展开按钮
   - 添加展开/收起动画效果

##### 样式优化
1. 建议卡片样式更新
   - 最小高度设置为 80px
   - 添加文本溢出处理
   - 优化卡片内容布局

2. 交互效果增强
   - 添加展开/收起按钮悬浮效果
   - 优化长文本展示动画
   - 保持与现有设计风格一致

##### 组件变更
1. 新增响应式数据
   ```typescript
   const expandedTextbook = ref<boolean>(false); // 控制建议教材展开状态
   ```

2. 新增工具方法
   ```typescript
   const isLongText = (text: string | undefined): boolean => {
     if (!text) return false;
     return text.length > 30; // 超过30个字符视为长文本
   };
   ```

3. 标签页更新
   - 添加"学习计划"标签页
   - 优化标签页切换逻辑

### 基础信息

#### 组件说明
1. 组件位置：`src/views/course/components/WordTestDrawer.vue`
2. 父组件传入参数：
   - visible: boolean (控制抽屉显示)
   - studentId: string (学生ID)
   - courseId: string (课程ID)

#### 数据接口
```typescript
interface TestDetailInfo {
  lastSemesterWordCollectRate: string;
  lastSemesterSuggestions: string;
  specialWordCollectRate: string;
  specialWordSuggestions: string;
  suggestTextbookName: string;
  suggestLearnTime: string;
  specialWordLearnSuggestions: string;
}
```

#### 注意事项
1. 长文本判断标准为 30 个字符，可根据实际需求调整
2. 所有新增内容都添加了空值处理，显示"暂无"
3. 保持了原有的动画和交互效果
4. 建议教材展开状态不会持久化，页面刷新后重置