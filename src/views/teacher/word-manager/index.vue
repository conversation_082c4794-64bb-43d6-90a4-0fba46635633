<template>
  <div class="app-container">
    <el-container class="h-screen">
      <!-- 左侧导航区 -->
      <el-aside width="320px" height="100vh" class="bg-white border-r">
        <MaterialTree
          @clickMaterialNode="clickMaterialNode"
          :openClass="false"
          :refreshTree="refreshTree"
        />
      </el-aside>

      <el-main class="main-class">
        <div>
          <el-form class="searchClass" @submit.prevent="handleSearch">
            <el-form-item label="教材" class="searchItem">
              <el-input
                v-model="bookName"
                placeholder=""
                :disabled="true"
                class="mr-4"
              >
              </el-input>
            </el-form-item>
            <el-form-item label="单元" class="searchItem">
              <el-input
                v-model="unitName"
                placeholder=""
                :disabled="true"
                class="mr-4"
              >
              </el-input>
            </el-form-item>
            <el-form-item label="单词" class="searchItem">
              <el-input
                v-model="searchKeyword"
                placeholder="请输入单词"
                class="mr-4"
              >
              </el-input>
            </el-form-item>
            <el-form-item class="searchItem">
              <el-button type="primary" @click="handleSearch">
                <el-icon><search /></el-icon>
                搜索
              </el-button>
            </el-form-item>
            <el-form-item class="searchItem">
              <el-button type="warning" @click="resetForm">
                <el-icon><search /></el-icon>
                重置
              </el-button>
            </el-form-item>
          </el-form>
        </div>

        <el-divider style="margin: 0px; padding-left: 20px" />

        <div class="word-card-container">
          <Lazyload v-if="initialized" :status="status" @next="next">
            <div
              v-for="workItem in words"
              :key="workItem.word"
              class="word-card"
            >
              <WordCard :item="workItem" @updateWordList="updateWordList" />
            </div>
          </Lazyload>
        </div>

        <!-- 状态提示信息 -->
        <div class="status-container">
          <i v-if="status === 'empty'" class="empty">{{ "暂无数据" }}</i>
          <!-- 数据全部加载完成 -->
          <i v-if="status === 'finished'" class="loaded">{{
            "我是有底线的"
          }}</i>
          <!-- 数据加载中 -->
          <i v-if="status === 'loading'" class="loading">数据加载中...</i>
          <!-- 数据加载错误 -->
          <i v-if="status === 'error'" class="loading">数据加载异常</i>
        </div>
      </el-main>
    </el-container>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";
import { Word, QueryParam, QryCondition, getWordList } from "../../../api/word";
import { WordCard, MaterialTree } from "./components";
import { Search } from "@element-plus/icons-vue";
import { Lazyload } from "../../../components/lazyload";
import { SkeletonScreen } from "../../../components/skeleton-screen";
const searchKeyword = ref("");
const pageNo = ref(0);
const initialized = ref(false);
const status = ref<"loading" | "finished" | "null" | "empty" | "error">("null");

// 分页参数
// const total = ref(0);
const queryParams = ref({
  textbookId: "",
  unitId: "",
  textBookType: "",
  textBookName: { value: "", compare: "LIKE" } as QryCondition,
  word: { value: "", compare: "LIKE" } as QryCondition,
  pageNum: 1,
  pageSize: 10,
});

const next = async () => {
  status.value = "loading";
  pageNo.value = pageNo.value + 1;
  queryParams.value.pageNum = pageNo.value;

  getWordList(queryParams.value)
    .then((res) => {
      if (res.code === 200) {
        const { total, rows } = res;
        words.value.push(...rows);

        if (words.value.length >= total) status.value = "finished";
        else status.value = "null";
        if (words.value.length === 0) status.value = "empty";
      }
    })
    .catch(() => {
      status.value = "error";
    })
    .finally(() => {
      initialized.value = true;
    });
};

const updateWordList = () => {
  console.log("updateCard111");
  fetchWordList();
};

onMounted(() => {
  next();
});

const fetchWordList = () => {
  pageNo.value = 0;
  words.value = [];
  status.value = "null";
  initialized.value = false;
  next();
};

const words = ref<Word[]>([]);
const bookName = ref("");
const unitName = ref("");
const refreshTree = ref(false);

const resetForm = () => {
  queryParams.value.textBookType = "";
  queryParams.value.textbookId = "";
  queryParams.value.unitId = "";
  queryParams.value.word = { value: "", compare: "LIKE" } as QryCondition;
  bookName.value = "";
  unitName.value = "";
  searchKeyword.value = "";
  fetchWordList();
  refreshTree.value = !refreshTree.value;
}

const handleSearch = () => {
  queryParams.value.word.value = searchKeyword.value;
  queryParams.value.word.compare = "LIKE";
  fetchWordList();
};
const clickMaterialNode = (nodeInfo: any) => {
  console.log("点击节点后回调：" + JSON.stringify(nodeInfo));
  bookName.value = nodeInfo.bookName;
  unitName.value = nodeInfo.unitName;
  searchKeyword.value = nodeInfo.word;

  queryParams.value = {
    textbookId: nodeInfo.textbookId,
    unitId: nodeInfo.unitId,
    textBookType: "",
    textBookName: { value: "", compare: "LIKE" } as QryCondition,
    word: { value: nodeInfo.word || '', compare: "EQ" } as QryCondition,
    pageNum: 1,
    pageSize: 10,
  };
  // if (nodeInfo.nodeType == 1) {
  //   queryParams.value.textbookId = nodeInfo.nodeId;
  // } else if (nodeInfo.nodeType == 2) {
  //   queryParams.value.unitId = nodeInfo.nodeId;
  // } else {
  //   queryParams.value.word.value = nodeInfo.word;
  //   queryParams.value.word.compare = "EQ";
  // }

  fetchWordList();
};

</script>

<style>
.app-container {
  height: 100%;
  background-color: #f3f4f6;
  .el-sub-menu__title {
    height: 35px;
  }
  .el-aside {
    border-right: 1px solid #e4e7ed;
    margin-bottom: 0px;
  }

  .h-screen {
    height: 100%;
  }

  .main-class {
    width: 100%;
    /* overflow: hidden; */
  }
  .searchClass {
    display: flex;
    flex-direction: row;
  }
  .searchItem {
    margin-left: 20px;
  }

  .word-card-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(310px, 1fr));
    gap: 16px;
    padding: 20px;
  }

  .word-card {
    background-color: #fff;
    border: 1px solid #dcdfe6;
    border-radius: 8px;
    padding: 12px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }

  .status-container {
    text-align: center;
    padding: 10px 0;
    margin-top: 10px;
    width: 100%;
    position: sticky;
    bottom: 0;
  }

  .empty,
  .loaded,
  .loading {
    display: inline-block;
    padding: 5px 10px;
    color: #909399;
    font-style: normal;
  }
}
</style>
