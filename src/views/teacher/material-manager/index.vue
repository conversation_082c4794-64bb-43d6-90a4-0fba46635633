<template>
  <div class="main-class">
    <div>
      <el-form class="searchClass" @submit.prevent="handleSearch">
        <el-form-item label="教材名称" class="searchItem">
          <el-input
            v-model="searchKeyword"
            placeholder="请输入教材名称"
            class="mr-4"
          >
          </el-input>
        </el-form-item>
        <el-form-item class="searchItem">
          <el-button type="primary" @click="handleSearch">
            <el-icon><search /></el-icon>
            搜索
          </el-button>
        </el-form-item>
        <el-form-item class="searchItem">
          <el-button type="warning" @click="handleAdd">新增教材</el-button>
        </el-form-item>
      </el-form>
    </div>

    <el-table :data="tableData" style="width: 100%">
      <el-table-column fixed prop="name" label="教材名称" width="350" />
      <el-table-column prop="typeName" label="教材类型" width="120" />
      <el-table-column prop="gradeName" label="年级" width="200" />
      <el-table-column prop="semesterName" label="学期" width="200" />
      <el-table-column prop="publisher" label="版本" width="200" />
      <el-table-column prop="required" label="必修" width="200" />
      <el-table-column prop="statUnitCnt" label="单元数量" width="100" />
      <el-table-column prop="statWordCnt" label="单词数量" width="100" />
      <el-table-column fixed="right" label="操作" min-width="120">
        <template #default="scope">
          <el-button
            link
            type="primary"
            size="small"
            @click="handleEdit(scope.row)"
          >
            编辑
          </el-button>
          <el-button
            link
            type="primary"
            size="small"
            @click="deletedText(scope.row.id)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <el-dialog
      v-model="dialogVisible"
      title="新增教材"
      width="60%"
      :before-close="cancelForm"
    >
      <el-form ref="formRef" :model="form" :rules="rules" label-position="top">
        <div class="dialog-content">
          <div class="dialog-top">
            <div class="cover-upload">
              <el-upload
                class="avatar-uploader"
                action="#"
                accept="image/*"
                :auto-upload="false"
                :show-file-list="false"
                :on-change="handleCoverChange"
              >
                <img v-if="imageUrl" :src="imageUrl" class="avatar" />
                <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
              </el-upload>
              <div class="upload-text">选择教材封面</div>
            </div>
            <div class="form-section">
              <el-form-item label="教材名称" prop="name">
                <el-input
                  v-model="form.name"
                  placeholder="请输入教材名称"
                ></el-input>
              </el-form-item>
              <el-form-item label="教材类型" prop="type">
                <el-select
                  v-model="form.type"
                  placeholder="请选择教材类型"
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in dictTypeOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="教材版本">
                <el-input
                  v-model="form.publisher"
                  placeholder="请输入教材版本"
                ></el-input>
              </el-form-item>
              <el-form-item label="年级">
                <el-select
                  v-model="form.grade"
                  placeholder="请选择年级"
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in gradeOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="学期">
                <el-select
                  v-model="form.semester"
                  placeholder="请选择学期"
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in semesterOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="必修">
                <el-input
                  v-model="form.required"
                  placeholder="请输入必修"
                ></el-input>
              </el-form-item>
            </div>
          </div>
          <div class="dialog-bottom">
            <div class="content-label">
              <span style="margin-right: 10px">教材内容</span>
              <el-tooltip effect="dark" placement="top">
                <template #content>
                  <div>
                    请按照格式输入教材内容，用>表示单元名称，特色教材和学生教材无需>。例子如下：<br />
                    学校教材案例：<br />
                    >unit-1<br />
                    Apple<br />
                    Banana<br />
                    >unit-2<br />
                    特色教材和学生教材案例：<br />
                    Apple<br />
                    Banana<br />
                    Cat
                  </div>
                </template>
                <el-icon><QuestionFilled /></el-icon>
              </el-tooltip>
            </div>
            <el-form-item prop="content">
              <el-input
                v-model="form.content"
                type="textarea"
                :rows="10"
                placeholder="教材格式为：用>表示单元名称，特色教材和学生教材无需>。例子如下：
             学校教材案例：
             >unit-1
             Apple
             Banana
             >unit-2
             特色教材和学生教材案例：
             Apple
             Banana
             Cat"
              ></el-input>
            </el-form-item>
          </div>
        </div>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="cancelForm">取消</el-button>
          <el-button type="primary" @click="submitForm">确定</el-button>
        </span>
      </template>
    </el-dialog>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="handlePagination"
    />
  </div>
</template>

<script setup lang="ts">
import {
  Textbook,
  addOrUpdateTextbook,
  getTextbookList,
  removeTextbook,
} from "../../../api/textbook";
import { ref, reactive, onMounted } from "vue";
import { Plus } from "@element-plus/icons-vue";
import { ElMessage, UploadFile } from "element-plus";
import Pagination from "@/components/Pagination";

const searchKeyword = ref("");
const handleSearch = () => {
  // 在这里添加搜索逻辑
  console.log("搜索关键字:", searchKeyword.value);
  queryParams.searchName = searchKeyword.value;
  fetchTextbookList();
};
const handleEdit = (row: Textbook) => {
  dialogVisible.value = true;
  // 重置表单
  Object.assign(form.value, row);
  imageUrl.value = row.cover;
  form.value.content = row.wordList;
};

// 表格数据
const tableData = ref<Textbook[]>([]);
const deletedIdList = ref<string[]>([]);

// 分页参数
const total = ref(0);
const queryParams = reactive({
  pageNum: 1,
  pageSize: 15,
  searchName: "",
});

// 获取教材列表
const fetchTextbookList = async () => {
  try {
    const res = await getTextbookList(queryParams);
    if (res.code === 200) {
      tableData.value = res.data.records;
      total.value = res.data.total;

      tableData.value.forEach((item) => {
        item.gradeName =
          gradeOptions.find((grade) => grade.value === item.grade)?.label || "";
        item.semesterName =
          semesterOptions.find((semester) => semester.value === item.semester)
            ?.label || "";
        item.typeName =
          dictTypeOptions.find((type) => type.value === item.type)?.label || "";
      });
    }
  } catch (error) {
    console.error("获取教材列表失败:", error);
  }
};

// 处理分页变化
const handlePagination = (pagination: any) => {
  queryParams.pageNum = pagination.page;
  queryParams.pageSize = pagination.limit;
  fetchTextbookList();
};

// 页面加载时获取教材列表
onMounted(() => {
  fetchTextbookList();
});

// 弹窗控制
const dialogVisible = ref(false);

// 封面图片URL
const imageUrl = ref("");

// 表单数据
const form = ref({
  id: "",
  name: "",
  type: "",
  // tags: '',
  content: "",
  publisher: "",
  grade: null,
  semester: null,
  required: "",
});

// 表单验证规则
const rules = {
  name: [{ required: true, message: "请输入教材名称", trigger: "blur" }],
  type: [{ required: true, message: "请选择教材类型", trigger: "change" }],
  content: [{ required: true, message: "请输入教材内容", trigger: "blur" }],
};

// 表单引用
const formRef = ref();
const imageFile = ref<UploadFile | null>(null);

// 教材类型选项
const dictTypeOptions = [
  { value: "1", label: "学校教材" },
  { value: "2", label: "特色教材" },
  { value: "3", label: "个性化教材" },
];

const gradeOptions = [
  { value: 1, label: "一年级" },
  { value: 2, label: "二年级" },
  { value: 3, label: "三年级" },
  { value: 4, label: "四年级" },
  { value: 5, label: "五年级" },
  { value: 6, label: "六年级" },
  { value: 7, label: "初一" },
  { value: 8, label: "初二" },
  { value: 9, label: "初三" },
  { value: 10, label: "高一" },
  { value: 11, label: "高二" },
  { value: 12, label: "高三" },
];

const semesterOptions = [
  { value: 1, label: "上学期" },
  { value: 2, label: "下学期" },
  { value: 3, label: "全年" },
];

// 处理封面图片上传
const handleCoverChange = (file: any) => {
  const isImage = file.raw.type.startsWith("image/");
  if (!isImage) {
    ElMessage.error("只能上传图片文件!");
    return;
  }

  // 创建临时URL用于预览
  imageUrl.value = URL.createObjectURL(file.raw);
  imageFile.value = file;
};

// 打开新增对话框
const handleAdd = () => {
  dialogVisible.value = true;
  // 重置表单
  form.value.name = "";
  form.value.type = "";
  // form.tags = '';
  form.value.content = "";
  form.value.publisher = "";
  form.value.grade = null;
  form.value.semester = null;
  form.value.required = "";
  imageUrl.value = "";
};

// 提交表单
const submitForm = async () => {
  if (!formRef.value) return;

  await formRef.value.validate(async (valid: boolean) => {
    if (valid) {
      try {
        // 处理表单数据
        const formData = new FormData();

        formData.append("name", form.value.name);
        formData.append("type", form.value.type);
        // formData.append('tags', form.tags);
        formData.append("wordList", form.value.content);
        if (imageFile.value) {
          formData.append("coverFile", imageFile.value?.raw || "");
        }
        formData.append("publisher", form.value.publisher);
        formData.append("grade", form.value.grade + "");
        formData.append("semester", form.value.semester + "");
        formData.append("required", form.value.required);
        formData.append("id", form.value.id);
        // 调用API提交数据
        const res = await addOrUpdateTextbook(formData);

        if (res.code === 200) {
          ElMessage.success("教材添加成功");
          dialogVisible.value = false;
          // 刷新教材列表
          fetchTextbookList();
        } else {
          ElMessage.error(res.msg || "添加失败");
        }
      } catch (error) {
        console.error("提交表单失败:", error);
        ElMessage.error("提交失败，请稍后重试");
      }
    } else {
      ElMessage.warning("请完善表单信息");
      return false;
    }
  });
};

// 取消提交
const cancelForm = () => {
  dialogVisible.value = false;
};

function deletedText(textbookId: string) {
  deletedIdList.value = [];
  deletedIdList.value.push(textbookId);

  removeTextbook(deletedIdList.value).then(() => {
    ElMessage.success("删除成功");
    fetchTextbookList();
  });
}
</script>
<style lang="css">
.main-class {
  margin: 10px;
}

.dialog-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.dialog-top {
  display: flex;
  gap: 20px;
}

.cover-upload {
  width: 200px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.avatar-uploader {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  width: 178px;
  height: 178px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.avatar-uploader:hover {
  border-color: #409eff;
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
}

.avatar {
  width: 178px;
  height: 178px;
  display: block;
}

.upload-text {
  margin-top: 10px;
  color: #606266;
}

.form-section {
  flex: 1;
}

.content-label {
  display: flex;
  align-items: center;
  font-weight: bold;
  margin-bottom: 10px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.searchClass {
  display: flex;
  flex-direction: row;
  align-items: center;
}
.searchItem {
  margin-right: 20px;
}
</style>
