# 教学小组和老师管理功能技术架构设计方案

## 1. 项目概述

基于现有的课表管理系统，为教学小组和老师管理功能设计完整的技术架构方案。该方案将充分复用现有的课表组件（`src/views/curriculum/`），遵循Vue 3 + Element Plus + Pinia技术栈，不引入额外插件。

### 1.1 目标路径
- 主要功能位置：`src/views/management/teacher/`
- 复用课表组件：`src/views/curriculum/components/`
- API位置：`src/api/management/`
- 状态管理：`src/stores/`

## 2. 需求分析总结

### 2.1 角色权限体系
- **管理员**：全部功能权限（创建教学组、创建老师、查看所有数据）
- **组长**：教学组管理权限（添加/移除老师、查看组内课表、排课/停课）
- **老师**：只读权限（查看自己的信息和课表）

### 2.2 核心功能模块
- **教学组管理**：创建、更换组长、添加/移除老师、删除教学组
- **老师管理**：创建老师、分配教学组、查看课表、排课/停课
- **权限控制**：基于角色的数据访问控制

## 3. 系统架构设计

```mermaid
graph TB
    subgraph "页面层 (src/views/management/teacher/)"
        A[教学组管理页面<br/>group/index.vue]
        B[老师管理页面<br/>teacher/index.vue]
        C[老师详情页面<br/>teacher/detail.vue]
    end
    
    subgraph "组件层"
        D[教学组列表<br/>GroupList.vue]
        E[老师列表<br/>TeacherList.vue]
        F[课表展示组件<br/>复用curriculum组件]
    end
    
    subgraph "复用课表组件 (src/views/curriculum/components/)"
        G[CourseCard.vue<br/>课程卡片]
        H[WeekView.vue<br/>周视图]
        I[MonthView.vue<br/>月视图]
        J[ScheduleDialog.vue<br/>排课弹窗]
    end
    
    subgraph "状态管理 (Pinia)"
        K[teacherGroupStore<br/>教学组状态]
        L[teacherManagementStore<br/>老师管理状态]
        M[curriculumStore<br/>课表状态复用]
    end
    
    subgraph "API层"
        N[教学组管理API]
        O[老师管理API]
        P[课表API复用]
    end
    
    A --> D
    B --> E
    C --> F
    D --> G
    E --> H
    F --> I
    D --> J
    
    A --> K
    B --> L
    C --> M
    
    K --> N
    L --> O
    M --> P
```

## 4. 数据模型设计

### 4.1 TypeScript 接口定义

```typescript
// src/views/management/teacher/types/index.ts

// 教学组数据模型
export interface TeachingGroup {
  id: string
  name: string
  subject?: string
  leaderId: string
  leaderName: string
  description?: string
  teacherCount: number
  status: 'active' | 'inactive'
  createdAt: string
  updatedAt: string
}

// 老师数据模型（扩展现有Student模型）
export interface Teacher {
  id: string
  name: string
  phone: string
  email?: string
  avatar?: string
  subjects?: string[]
  groupId?: string
  groupName?: string
  role: 'admin' | 'group_leader' | 'teacher'
  status: 'active' | 'inactive'
  joinedAt?: string
  createdAt: string
}

// 教学组成员关系
export interface GroupMembership {
  id: string
  groupId: string
  teacherId: string
  role: 'leader' | 'member'
  joinedAt: string
}

// 权限数据模型
export interface TeacherPermission {
  userId: string
  role: 'admin' | 'group_leader' | 'teacher'
  canManageGroups: boolean
  canManageTeachers: boolean
  canViewAllSchedules: boolean
  canScheduleForOthers: boolean
  managedGroupIds: string[]
}

// API请求参数
export interface GetTeachingGroupsParams {
  keyword?: string
  status?: 'active' | 'inactive'
  pageNum?: number
  pageSize?: number
}

export interface CreateTeachingGroupRequest {
  name: string
  subject?: string
  leaderId: string
  description?: string
}

export interface GetTeachersParams {
  keyword?: string
  groupId?: string
  role?: string
  status?: 'active' | 'inactive'
  pageNum?: number
  pageSize?: number
}

export interface CreateTeacherRequest {
  name: string
  phone: string
  email?: string
  subjects?: string[]
  role: 'teacher' | 'group_leader'
  groupId?: string
}

// 复用现有课表相关类型
export type { 
  CourseSchedule, 
  ScheduleRequest, 
  CancelCourseData 
} from '@/views/curriculum/types/curriculum'
```

### 4.2 API接口设计

```typescript
// src/api/management/types.ts

import type { ApiResponse, PageResponse } from '@/api/curriculum/types'
import type { 
  TeachingGroup, 
  Teacher, 
  GetTeachingGroupsParams,
  CreateTeachingGroupRequest,
  GetTeachersParams,
  CreateTeacherRequest,
  TeacherPermission
} from '@/views/management/teacher/types'

// 教学组管理API
export interface TeachingGroupAPI {
  // 获取教学组列表
  getGroups(params: GetTeachingGroupsParams): Promise<ApiResponse<PageResponse<TeachingGroup>>>
  
  // 创建教学组
  createGroup(data: CreateTeachingGroupRequest): Promise<ApiResponse<void>>
  
  // 更新教学组
  updateGroup(id: string, data: Partial<CreateTeachingGroupRequest>): Promise<ApiResponse<void>>
  
  // 删除教学组
  deleteGroup(id: string): Promise<ApiResponse<void>>
  
  // 更换组长
  changeGroupLeader(groupId: string, newLeaderId: string): Promise<ApiResponse<void>>
  
  // 添加老师到教学组
  addTeacherToGroup(groupId: string, teacherId: string): Promise<ApiResponse<void>>
  
  // 从教学组移除老师
  removeTeacherFromGroup(groupId: string, teacherId: string): Promise<ApiResponse<void>>
  
  // 获取教学组成员
  getGroupMembers(groupId: string): Promise<ApiResponse<Teacher[]>>
}

// 老师管理API
export interface TeacherManagementAPI {
  // 获取老师列表
  getTeachers(params: GetTeachersParams): Promise<ApiResponse<PageResponse<Teacher>>>
  
  // 创建老师
  createTeacher(data: CreateTeacherRequest): Promise<ApiResponse<void>>
  
  // 更新老师信息
  updateTeacher(id: string, data: Partial<CreateTeacherRequest>): Promise<ApiResponse<void>>
  
  // 删除老师
  deleteTeacher(id: string): Promise<ApiResponse<void>>
  
  // 分配老师到教学组
  assignTeacherToGroup(teacherId: string, groupId: string): Promise<ApiResponse<void>>
  
  // 获取老师权限
  getTeacherPermissions(teacherId: string): Promise<ApiResponse<TeacherPermission>>
}
```

## 5. 文件目录结构

```
src/views/management/teacher/
├── index.vue                          # 老师管理主页面
├── group/
│   ├── index.vue                      # 教学组管理主页面
│   └── components/
│       ├── GroupList.vue              # 教学组列表组件
│       ├── GroupForm.vue              # 教学组表单组件
│       ├── GroupDetail.vue            # 教学组详情组件
│       ├── GroupMemberManager.vue     # 组成员管理组件
│       └── GroupScheduleView.vue      # 组课表汇总（复用curriculum组件）
├── components/
│   ├── TeacherList.vue                # 老师列表组件
│   ├── TeacherForm.vue                # 老师表单组件
│   ├── TeacherDetail.vue              # 老师详情组件
│   ├── TeacherSelector.vue            # 老师选择器
│   ├── GroupSelector.vue              # 教学组选择器
│   └── PermissionGuard.vue            # 权限守卫组件
├── types/
│   └── index.ts                       # 类型定义
├── composables/
│   ├── useTeacherPermissions.ts       # 权限管理组合式API
│   ├── useTeacherManagement.ts        # 老师管理组合式API
│   └── useGroupManagement.ts          # 教学组管理组合式API
└── 教学小组和老师管理需求.md
└── 教学小组和老师管理技术架构设计方案.md

src/api/management/
├── index.ts                           # API统一导出
├── types.ts                           # API类型定义
├── teachingGroup.ts                   # 教学组API实现
└── teacher.ts                         # 老师管理API实现

src/stores/
├── teacherGroup.js                    # 教学组状态管理
└── teacherManagement.js               # 老师管理状态管理
```

## 6. 组件设计方案

### 6.1 核心页面组件

#### 6.1.1 教学组管理页面 (group/index.vue)
```vue
<template>
  <div class="teaching-group-management">
    <!-- 搜索和操作栏 -->
    <div class="operation-bar">
      <el-input v-model="searchKeyword" placeholder="搜索教学组" />
      <el-button type="primary" @click="showCreateDialog">创建教学组</el-button>
    </div>
    
    <!-- 教学组列表 -->
    <GroupList 
      :data="groups" 
      :loading="loading"
      @edit="handleEdit"
      @delete="handleDelete"
      @view-members="handleViewMembers"
      @view-schedule="handleViewSchedule"
    />
    
    <!-- 分页 -->
    <el-pagination />
    
    <!-- 创建/编辑弹窗 -->
    <GroupForm 
      v-model:visible="formVisible"
      :data="currentGroup"
      @success="handleFormSuccess"
    />
  </div>
</template>
```

#### 6.1.2 老师管理页面 (index.vue)
```vue
<template>
  <div class="teacher-management">
    <!-- 操作栏 -->
    <div class="operation-bar">
      <el-input v-model="searchKeyword" placeholder="搜索老师" />
      <GroupSelector v-model="selectedGroupId" />
      <el-button type="primary" @click="showCreateDialog">创建老师</el-button>
    </div>
    
    <!-- 老师列表 -->
    <TeacherList 
      :data="teachers"
      :loading="loading"
      @edit="handleEdit"
      @delete="handleDelete"
      @view-schedule="handleViewSchedule"
      @assign-group="handleAssignGroup"
    />
    
    <!-- 老师表单弹窗 -->
    <TeacherForm 
      v-model:visible="formVisible"
      :data="currentTeacher"
      @success="handleFormSuccess"
    />
  </div>
</template>
```

### 6.2 复用课表组件的集成方案

```vue
<!-- GroupScheduleView.vue - 组课表汇总 -->
<template>
  <div class="group-schedule-view">
    <!-- 复用现有的视图切换和操作 -->
    <div class="schedule-header">
      <el-radio-group v-model="currentView">
        <el-radio-button label="week">周视图</el-radio-button>
        <el-radio-button label="month">月视图</el-radio-button>
      </el-radio-group>
      
      <!-- 老师筛选 -->
      <TeacherSelector 
        v-model="selectedTeacherId" 
        :group-id="groupId"
        placeholder="选择老师查看课表"
      />
    </div>
    
    <!-- 复用现有课表组件 -->
    <WeekView 
      v-if="currentView === 'week'"
      :schedules="filteredSchedules"
      :selected-date="selectedDate"
      @course-action="handleCourseAction"
    />
    
    <MonthView 
      v-else
      :schedules="filteredSchedules"
      :selected-date="selectedDate"
    />
    
    <!-- 复用排课弹窗 -->
    <ScheduleDialog 
      v-model:visible="scheduleDialogVisible"
      :teacher-id="selectedTeacherId"
      @success="handleScheduleSuccess"
    />
  </div>
</template>
```

## 7. 状态管理设计

### 7.1 教学组状态管理 (teacherGroup.js)

```javascript
// src/stores/teacherGroup.js
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { teachingGroupApi } from '@/api/management'

export const useTeacherGroupStore = defineStore('teacherGroup', () => {
  // 状态
  const groups = ref([])
  const currentGroup = ref(null)
  const groupMembers = ref([])
  const loading = ref(false)
  const pagination = ref({
    pageNum: 1,
    pageSize: 10,
    total: 0
  })

  // 计算属性
  const groupOptions = computed(() => 
    groups.value.map(group => ({
      label: group.name,
      value: group.id
    }))
  )

  // 方法
  const fetchGroups = async (params = {}) => {
    loading.value = true
    try {
      const response = await teachingGroupApi.getGroups({
        ...params,
        pageNum: pagination.value.pageNum,
        pageSize: pagination.value.pageSize
      })
      if (response.code === 200) {
        groups.value = response.data.rows
        pagination.value.total = response.data.total
      }
    } catch (error) {
      ElMessage.error('获取教学组列表失败')
    } finally {
      loading.value = false
    }
  }

  const createGroup = async (data) => {
    try {
      const response = await teachingGroupApi.createGroup(data)
      if (response.code === 200) {
        ElMessage.success('创建教学组成功')
        await fetchGroups()
        return true
      }
    } catch (error) {
      ElMessage.error('创建教学组失败')
      return false
    }
  }

  // 其他方法...
  
  return {
    groups,
    currentGroup,
    groupMembers,
    loading,
    pagination,
    groupOptions,
    fetchGroups,
    createGroup,
    // 其他方法...
  }
})
```

### 7.2 老师管理状态管理 (teacherManagement.js)

```javascript
// src/stores/teacherManagement.js
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { teacherManagementApi } from '@/api/management'
import { useCurriculumStore } from './curriculum'

export const useTeacherManagementStore = defineStore('teacherManagement', () => {
  // 状态
  const teachers = ref([])
  const currentTeacher = ref(null)
  const permissions = ref(null)
  const loading = ref(false)

  // 复用课表store
  const curriculumStore = useCurriculumStore()

  // 计算属性
  const teacherOptions = computed(() => 
    teachers.value.map(teacher => ({
      label: teacher.name,
      value: teacher.id
    }))
  )

  // 方法
  const fetchTeachers = async (params = {}) => {
    // 实现逻辑
  }

  const createTeacher = async (data) => {
    // 实现逻辑
  }

  // 集成课表相关功能
  const getTeacherSchedule = async (teacherId, params) => {
    return await curriculumStore.fetchSchedules({
      ...params,
      teacherId
    })
  }

  const scheduleForTeacher = async (teacherId, scheduleData) => {
    return await curriculumStore.createSchedule({
      ...scheduleData,
      teacherId
    })
  }

  return {
    teachers,
    currentTeacher,
    permissions,
    loading,
    teacherOptions,
    fetchTeachers,
    createTeacher,
    getTeacherSchedule,
    scheduleForTeacher,
    // 其他方法...
  }
})
```

## 8. 路由配置

```javascript
// 在 src/router/index.js 中添加路由配置
const managementRoutes = {
  path: '/management',
  component: Layout,
  redirect: '/management/teacher',
  name: 'Management',
  meta: {
    title: '教学管理',
    icon: 'peoples'
  },
  children: [
    {
      path: 'teacher',
      component: () => import('@/views/management/teacher/index'),
      name: 'TeacherManagement',
      meta: {
        title: '老师管理',
        icon: 'user',
        permissions: ['management:teacher:list']
      }
    },
    {
      path: 'teacher/group',
      component: () => import('@/views/management/teacher/group/index'),
      name: 'TeachingGroupManagement',
      meta: {
        title: '教学组管理',
        icon: 'people',
        permissions: ['management:group:list']
      }
    },
    {
      path: 'teacher/group/:groupId/schedule',
      component: () => import('@/views/management/teacher/group/components/GroupScheduleView'),
      name: 'GroupSchedule',
      meta: {
        title: '组课表查看',
        activeMenu: '/management/teacher/group',
        permissions: ['management:group:schedule:view']
      }
    }
  ]
}
```

## 9. 权限控制方案

### 9.1 权限组合式API

```typescript
// src/views/management/teacher/composables/useTeacherPermissions.ts
import { computed } from 'vue'
import { useUserStore } from '@/store/modules/user'
import { useTeacherManagementStore } from '@/stores/teacherManagement'

export const useTeacherPermissions = () => {
  const userStore = useUserStore()
  const teacherStore = useTeacherManagementStore()

  const currentUser = computed(() => userStore)
  const permissions = computed(() => teacherStore.permissions)

  // 检查基础权限
  const hasPermission = (permission: string) => {
    return currentUser.value.permissions.includes(permission)
  }

  // 检查是否可以管理教学组
  const canManageGroup = (groupId?: string) => {
    if (hasPermission('management:group:manage')) return true
    if (!permissions.value || !groupId) return false
    return permissions.value.managedGroupIds.includes(groupId)
  }

  // 检查是否可以管理老师
  const canManageTeacher = (teacherId?: string) => {
    if (hasPermission('management:teacher:manage')) return true
    // 组长只能管理本组老师的逻辑
    return false
  }

  // 检查是否可以排课
  const canScheduleForTeacher = (teacherId: string) => {
    if (hasPermission('curriculum:schedule:manage')) return true
    return canManageTeacher(teacherId)
  }

  return {
    hasPermission,
    canManageGroup,
    canManageTeacher,
    canScheduleForTeacher
  }
}
```

### 9.2 权限守卫组件

```vue
<!-- src/views/management/teacher/components/PermissionGuard.vue -->
<template>
  <div v-if="hasAccess">
    <slot />
  </div>
  <div v-else-if="showFallback">
    <slot name="fallback">
      <el-empty description="暂无权限访问" />
    </slot>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useTeacherPermissions } from '../composables/useTeacherPermissions'

const props = defineProps({
  permission: String,
  groupId: String,
  teacherId: String,
  showFallback: {
    type: Boolean,
    default: true
  }
})

const { hasPermission, canManageGroup, canManageTeacher } = useTeacherPermissions()

const hasAccess = computed(() => {
  if (props.permission) {
    return hasPermission(props.permission)
  }
  if (props.groupId) {
    return canManageGroup(props.groupId)
  }
  if (props.teacherId) {
    return canManageTeacher(props.teacherId)
  }
  return true
})
</script>
```

## 10. 关键功能实现要点

### 10.1 课表组件复用策略

1. **直接复用组件**：`CourseCard`, `WeekView`, `MonthView`, `ScheduleDialog`
2. **扩展现有Store**：在 `curriculumStore` 中添加教学组相关方法
3. **传参适配**：通过props传递不同的teacherId来适配不同场景

### 10.2 排课冲突检测

```typescript
// 复用现有课表功能，在排课时检测冲突
const checkScheduleConflict = (newSchedule: ScheduleRequest, existingSchedules: CourseSchedule[]) => {
  // 实现时间冲突检测逻辑
  return {
    hasConflict: boolean,
    conflictedSchedules: CourseSchedule[]
  }
}
```

### 10.3 批量操作

```typescript
// 在组件中实现批量选择和操作
const batchAssignGroup = async (teacherIds: string[], groupId: string) => {
  // 批量分配教学组
}

const batchSchedule = async (teacherIds: string[], scheduleData: ScheduleRequest) => {
  // 批量排课
}
```

## 11. 开发实施计划

```mermaid
gantt
    title 教学小组和老师管理功能开发计划
    dateFormat  YYYY-MM-DD
    section 第一阶段：基础架构
    数据模型和API设计      :done, phase1, 2024-01-01, 3d
    状态管理架构搭建       :active, phase2, 2024-01-04, 2d
    
    section 第二阶段：核心功能
    教学组管理功能开发     :phase3, 2024-01-06, 5d
    老师管理功能开发       :phase4, 2024-01-11, 5d
    权限控制系统实现       :phase5, 2024-01-16, 3d
    
    section 第三阶段：课表集成
    课表组件复用集成       :phase6, 2024-01-19, 4d
    排课功能扩展          :phase7, 2024-01-23, 3d
    组课表汇总功能        :phase8, 2024-01-26, 3d
    
    section 第四阶段：测试上线
    功能测试和Bug修复     :phase9, 2024-01-29, 4d
    用户体验优化          :phase10, 2024-02-02, 2d
    文档编写和部署        :phase11, 2024-02-04, 2d
```

## 12. 技术要点总结

1. **路径调整**：按照用户要求调整到正确的 `src/views/management/teacher/` 路径
2. **组件复用**：最大化复用现有的课表组件，减少重复开发
3. **无额外插件**：严格使用现有技术栈，不引入新的依赖
4. **权限控制**：基于角色的细粒度权限控制
5. **数据一致性**：与现有课表数据模型保持一致性
6. **用户体验**：保持与现有课表功能一致的交互体验

该方案充分考虑了现有系统架构，最大化代码复用，确保开发效率和系统一致性。