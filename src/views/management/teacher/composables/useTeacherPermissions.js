import { computed } from 'vue'
import useUserStore from '@/store/modules/user'
import { useTeacherManagementStore } from '@/stores/teacherManagement'

export const useTeacherPermissions = () => {
  const userStore = useUserStore()
  const teacherStore = useTeacherManagementStore()

  const currentUser = computed(() => ({
    id: userStore.id,
    name: userStore.name,
    permissions: userStore.permissions,
    roles: userStore.roles
  }))
  const permissions = computed(() => teacherStore.permissions)

  // 检查基础权限
  const hasPermission = (permission) => {
    if (!currentUser.value.permissions) return false
    return currentUser.value.permissions.includes(permission)
  }

  // 检查是否可以管理教学组
  const canManageGroup = (groupId) => {
    return true
    // 管理员有全部权限
    if (hasPermission('management:group:manage')) return true
    
    // 检查是否为组长并且管理指定的组
    if (!permissions.value || !groupId) return false
    return permissions.value.managedGroupIds.includes(groupId)
  }

  // 检查是否可以管理老师
  const canManageTeacher = (teacher) => {
    return true
    // 管理员有全部权限
    if (hasPermission('management:teacher:manage')) return true
    
    // 组长只能管理本组老师
    if (!permissions.value || !teacher?.groupId) return false
    return permissions.value.managedGroupIds.includes(teacher.groupId)
  }

  // 检查是否可以排课
  const canScheduleForTeacher = (teacher) => {
    return true
    // 管理员有全部权限
    if (hasPermission('curriculum:schedule:manage')) return true
    
    // 组长可以为本组老师排课
    return canManageTeacher(teacher)
  }

  // 检查是否可以停课
  const canCancelCourse = (course) => {
    return true
    // 管理员有全部权限
    if (hasPermission('curriculum:course:cancel')) return true
    
    // 组长可以停止本组老师的课程
    if (!permissions.value || !course?.teacherId) return false
    
    // 需要通过老师信息判断是否在管理的组内
    const teacher = teacherStore.teachers.find(t => t.id === course.teacherId)
    return canManageTeacher(teacher)
  }

  // 检查是否可以查看课表
  const canViewSchedule = (teacher) => {
    // 管理员可以查看所有课表
    if (hasPermission('curriculum:schedule:view')) return true
    
    // 组长可以查看本组老师课表
    if (canManageTeacher(teacher)) return true
    
    // 老师可以查看自己的课表
    return currentUser.value.id === teacher?.id
  }

  // 检查是否可以创建老师
  const canCreateTeacher = () => {
    return true
    return hasPermission('management:teacher:create')
  }

  // 检查是否可以删除老师
  const canDeleteTeacher = (teacher) => {
    return true
    // 管理员可以删除任何老师
    if (hasPermission('management:teacher:delete')) return true
    
    // 组长可以删除本组普通老师（不能删除其他组长）
    if (teacher?.role === 'group_leader') return false
    return canManageTeacher(teacher)
  }

  // 检查是否可以编辑老师
  const canEditTeacher = (teacher) => {
    return true
    // 管理员可以编辑任何老师
    if (hasPermission('management:teacher:edit')) return true
    
    // 组长可以编辑本组老师的基本信息
    return canManageTeacher(teacher)
  }

  // 检查是否可以分配教学组
  const canAssignGroup = (teacher) => {
    return true
    // 管理员可以分配任何老师到任何组
    if (hasPermission('management:teacher:assign')) return true
    
    // 组长不能重新分配老师的教学组
    return false
  }

  // 检查是否可以批量操作
  const canBatchOperation = () => {
    return hasPermission('management:teacher:batch')
  }

  // 获取当前用户角色
  const getCurrentUserRole = () => {
    if (hasPermission('management:admin')) return 'admin'
    if (hasPermission('management:group:leader')) return 'group_leader'
    return 'teacher'
  }

  // 获取可管理的教学组IDs
  const getManagedGroupIds = () => {
    if (hasPermission('management:group:manage')) {
      // 管理员可以管理所有组
      return null // null 表示全部
    }
    return permissions.value?.managedGroupIds || []
  }

  return {
    hasPermission,
    canManageGroup,
    canManageTeacher,
    canScheduleForTeacher,
    canCancelCourse,
    canViewSchedule,
    canCreateTeacher,
    canDeleteTeacher,
    canEditTeacher,
    canAssignGroup,
    canBatchOperation,
    getCurrentUserRole,
    getManagedGroupIds
  }
}