<template>
  <div class="teacher-management">
    <!-- 操作栏 -->
    <div class="operation-bar">
      <div class="search-section">
        <el-input
          v-model="searchForm.keyword"
          placeholder="搜索老师姓名或手机号"
          style="width: 300px; margin-right: 12px;"
          clearable
          @input="handleSearch"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
        
        <el-select
          v-model="searchForm.groupId"
          placeholder="选择教学组"
          style="width: 200px; margin-right: 12px;"
          clearable
          @change="handleSearch"
        >
          <el-option label="全部教学组" value="" />
          <el-option
            v-for="group in groupOptions"
            :key="group.value"
            :label="group.label"
            :value="group.value"
          />
        </el-select>
        
        <el-select
          v-model="searchForm.role"
          placeholder="选择角色"
          style="width: 150px; margin-right: 12px;"
          clearable
          @change="handleSearch"
        >
          <el-option label="全部角色" value="" />
          <el-option label="管理员" value="admin" />
          <el-option label="组长" value="group_leader" />
          <el-option label="老师" value="teacher" />
        </el-select>
        
        <el-button type="primary" @click="handleSearch">
          <el-icon><Search /></el-icon>
          搜索
        </el-button>
        
        <el-button @click="handleReset">
          <el-icon><Refresh /></el-icon>
          重置
        </el-button>
      </div>
      
      <div class="action-section">
        <el-button 
          type="primary" 
          @click="handleCreate"
          v-if="hasPermission('management:teacher:create')"
        >
          <el-icon><Plus /></el-icon>
          创建老师
        </el-button>
        
        <el-button 
          type="success" 
          :disabled="selectedTeachers.length === 0"
          @click="handleBatchAssignGroup"
          v-if="hasPermission('management:teacher:batch')"
        >
          <el-icon><Connection /></el-icon>
          批量分配教学组
        </el-button>
      </div>
    </div>

    <!-- 老师列表 -->
    <TeacherList 
      :data="teachers"
      :loading="loading"
      :selected="selectedTeachers"
      @selection-change="handleSelectionChange"
      @edit="handleEdit"
      @delete="handleDelete"
      @view-schedule="handleViewSchedule"
      @assign-group="handleAssignGroup"
    />

    <!-- 分页 -->
    <div class="pagination-wrapper">
      <el-pagination
        v-model:current-page="pagination.pageNum"
        v-model:page-size="pagination.pageSize"
        :total="pagination.total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 创建/编辑老师弹窗 -->
    <TeacherForm 
      v-model:visible="formVisible"
      :data="currentTeacher"
      :group-options="groupOptions"
      @success="handleFormSuccess"
    />

    <!-- 批量分配教学组弹窗 -->
    <BatchAssignGroupDialog
      v-model:visible="batchDialogVisible"
      :teacher-ids="selectedTeacherIds"
      :group-options="groupOptions"
      @success="handleBatchSuccess"
    />

    <!-- 老师课表查看弹窗 -->
    <TeacherScheduleDialog
      v-model:visible="scheduleDialogVisible"
      :teacher="scheduleTeacher"
    />
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Refresh, Plus, Connection } from '@element-plus/icons-vue'
import { useTeacherManagementStore } from '@/stores/teacherManagement'
import { useTeacherGroupStore } from '@/stores/teacherGroup'
import { useTeacherPermissions } from './composables/useTeacherPermissions'
import TeacherList from './components/TeacherList.vue'
import TeacherForm from './components/TeacherForm.vue'
import BatchAssignGroupDialog from './components/BatchAssignGroupDialog.vue'
import TeacherScheduleDialog from './components/TeacherScheduleDialog.vue'

// 状态管理
const teacherStore = useTeacherManagementStore()
const groupStore = useTeacherGroupStore()
const { hasPermission } = useTeacherPermissions()

// 响应式数据
const searchForm = reactive({
  keyword: '',
  groupId: '',
  role: '',
  status: 'active'
})

const formVisible = ref(false)
const batchDialogVisible = ref(false)
const scheduleDialogVisible = ref(false)
const currentTeacher = ref(null)
const scheduleTeacher = ref(null)
const selectedTeachers = ref([])

// 计算属性
const teachers = computed(() => teacherStore.teachers)
const loading = computed(() => teacherStore.loading)
const pagination = computed(() => teacherStore.pagination)
const groupOptions = computed(() => groupStore.groupOptions)

const selectedTeacherIds = computed(() => 
  selectedTeachers.value.map(teacher => teacher.id)
)

// 方法
const fetchData = async () => {
  await teacherStore.fetchTeachers(searchForm)
}

const fetchGroups = async () => {
  await groupStore.fetchGroups({ status: 'active' })
}

const handleSearch = () => {
  teacherStore.resetPagination()
  fetchData()
}

const handleReset = () => {
  Object.assign(searchForm, {
    keyword: '',
    groupId: '',
    role: '',
    status: 'active'
  })
  handleSearch()
}

const handleCreate = () => {
  currentTeacher.value = null
  formVisible.value = true
}

const handleEdit = (teacher) => {
  currentTeacher.value = { ...teacher }
  formVisible.value = true
}

const handleDelete = async (teacher) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除老师"${teacher.name}"吗？`,
      '确认删除',
      {
        confirmButtonText: '删除',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const success = await teacherStore.deleteTeacher(teacher.id)
    if (success) {
      // 清空选中状态
      selectedTeachers.value = []
    }
  } catch (error) {
    // 用户取消删除
  }
}

const handleViewSchedule = (teacher) => {
  scheduleTeacher.value = teacher
  scheduleDialogVisible.value = true
}

const handleAssignGroup = async (teacher, groupId) => {
  const success = await teacherStore.assignTeacherToGroup(teacher.id, groupId)
  if (success) {
    // 更新教学组数据
    await fetchGroups()
  }
}

const handleBatchAssignGroup = () => {
  if (selectedTeachers.value.length === 0) {
    ElMessage.warning('请先选择要分配的老师')
    return
  }
  batchDialogVisible.value = true
}

const handleSelectionChange = (selection) => {
  selectedTeachers.value = selection
}

const handleSizeChange = (size) => {
  teacherStore.pagination.pageSize = size
  teacherStore.resetPagination()
  fetchData()
}

const handleCurrentChange = (page) => {
  teacherStore.setPagination(page)
  fetchData()
}

const handleFormSuccess = () => {
  formVisible.value = false
  currentTeacher.value = null
  fetchData()
  // 更新教学组数据（可能创建了新组长）
  fetchGroups()
}

const handleBatchSuccess = () => {
  batchDialogVisible.value = false
  selectedTeachers.value = []
  fetchData()
  fetchGroups()
}

// 生命周期
onMounted(async () => {
  console.log('🔍 [DEBUG] Teacher Management - 组件挂载开始')
  console.log('🔍 [DEBUG] teacherStore:', teacherStore)
  console.log('🔍 [DEBUG] groupStore:', groupStore)
  console.log('🔍 [DEBUG] hasPermission function:', hasPermission)
  
  try {
    await Promise.all([
      fetchData(),
      fetchGroups()
    ])
    console.log('✅ [DEBUG] 数据加载完成')
    console.log('🔍 [DEBUG] teachers:', teachers.value)
    console.log('🔍 [DEBUG] groups:', groupStore.groups)
  } catch (error) {
    console.error('❌ [DEBUG] 数据加载失败:', error)
  }
})
</script>

<style scoped>
.teacher-management {
  padding: 20px;
}

.operation-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 16px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.search-section {
  display: flex;
  align-items: center;
}

.action-section {
  display: flex;
  align-items: center;
  gap: 12px;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
  padding: 20px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
</style>