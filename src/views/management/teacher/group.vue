<template>
  <div class="group-management">
    <!-- 操作栏 -->
    <div class="operation-bar">
      <div class="search-section">
        <el-input
          v-model="searchForm.keyword"
          placeholder="搜索教学组名称"
          style="width: 300px; margin-right: 12px;"
          clearable
          @input="handleSearch"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
        
        <el-select
          v-model="searchForm.status"
          placeholder="选择状态"
          style="width: 150px; margin-right: 12px;"
          @change="handleSearch"
        >
          <el-option label="全部状态" value="" />
          <el-option label="启用" value="active" />
          <el-option label="禁用" value="inactive" />
        </el-select>
        
        <el-button type="primary" @click="handleSearch">
          <el-icon><Search /></el-icon>
          搜索
        </el-button>
        
        <el-button @click="handleReset">
          <el-icon><Refresh /></el-icon>
          重置
        </el-button>
      </div>
      
      <div class="action-section">
        <el-button 
          type="primary" 
          @click="handleCreate"
          v-if="hasPermission('management:group:create')"
        >
          <el-icon><Plus /></el-icon>
          创建教学组
        </el-button>
      </div>
    </div>

    <!-- 教学组卡片列表 -->
    <div class="group-cards" v-loading="loading">
      <el-row :gutter="20">
        <el-col 
          v-for="group in groups" 
          :key="group.id" 
          :span="8" 
          :lg="8" 
          :md="12" 
          :sm="24"
          style="margin-bottom: 20px;"
        >
          <GroupCard
            :group="group"
            @edit="handleEdit"
            @delete="handleDelete"
            @change-leader="handleChangeLeader"
            @manage-members="handleManageMembers"
            @view-schedule="handleViewGroupSchedule"
          />
        </el-col>
      </el-row>

      <!-- 空状态 -->
      <el-empty
        v-if="!loading && groups.length === 0"
        :image-size="200"
        description="暂无教学组数据"
      >
        <el-button 
          type="primary" 
          @click="handleCreate"
          v-if="hasPermission('management:group:create')"
        >
          创建第一个教学组
        </el-button>
      </el-empty>
    </div>

    <!-- 分页 -->
    <div class="pagination-wrapper" v-if="groups.length > 0">
      <el-pagination
        v-model:current-page="pagination.pageNum"
        v-model:page-size="pagination.pageSize"
        :total="pagination.total"
        :page-sizes="[9, 18, 36]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 创建/编辑教学组弹窗 -->
    <GroupForm 
      v-model:visible="formVisible"
      :data="currentGroup"
      @success="handleFormSuccess"
    />

    <!-- 成员管理弹窗 -->
    <GroupMembersDialog
      v-model:visible="membersDialogVisible"
      :group="selectedGroup"
      @success="handleMembersSuccess"
    />

    <!-- 更换组长弹窗 -->
    <ChangeLeaderDialog
      v-model:visible="changeLeaderVisible"
      :group="selectedGroup"
      @success="handleChangeLeaderSuccess"
    />

    <!-- 教学组课表查看弹窗 -->
    <GroupScheduleDialog
      v-model:visible="scheduleDialogVisible"
      :group="selectedGroup"
    />
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Refresh, Plus } from '@element-plus/icons-vue'
import { useTeacherGroupStore } from '@/stores/teacherGroup'
import { useTeacherPermissions } from './composables/useTeacherPermissions'
import GroupCard from './components/GroupCard.vue'
import GroupForm from './components/GroupForm.vue'
import GroupMembersDialog from './components/GroupMembersDialog.vue'
import ChangeLeaderDialog from './components/ChangeLeaderDialog.vue'
import GroupScheduleDialog from './components/GroupScheduleDialog.vue'

// 状态管理
const groupStore = useTeacherGroupStore()
const { hasPermission } = useTeacherPermissions()

// 响应式数据
const searchForm = reactive({
  keyword: '',
  status: 'active'
})

const formVisible = ref(false)
const membersDialogVisible = ref(false)
const changeLeaderVisible = ref(false)
const scheduleDialogVisible = ref(false)
const currentGroup = ref(null)
const selectedGroup = ref(null)

// 计算属性
const groups = computed(() => groupStore.groups)
const loading = computed(() => groupStore.loading)
const pagination = computed(() => groupStore.pagination)

// 方法
const fetchData = async () => {
  await groupStore.fetchGroups(searchForm)
}

const handleSearch = () => {
  groupStore.resetPagination()
  fetchData()
}

const handleReset = () => {
  Object.assign(searchForm, {
    keyword: '',
    status: 'active'
  })
  handleSearch()
}

const handleCreate = () => {
  currentGroup.value = null
  formVisible.value = true
}

const handleEdit = (group) => {
  currentGroup.value = { ...group }
  formVisible.value = true
}

const handleDelete = async (group) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除教学组"${group.name}"吗？删除后该组的老师将变为未分配状态。`,
      '确认删除',
      {
        confirmButtonText: '删除',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await groupStore.deleteGroup(group.id)
  } catch (error) {
    // 用户取消删除
  }
}

const handleChangeLeader = (group) => {
  selectedGroup.value = group
  changeLeaderVisible.value = true
}

const handleManageMembers = (group) => {
  selectedGroup.value = group
  membersDialogVisible.value = true
}

const handleViewGroupSchedule = (group) => {
  selectedGroup.value = group
  scheduleDialogVisible.value = true
}

const handleSizeChange = (size) => {
  groupStore.pagination.pageSize = size
  groupStore.resetPagination()
  fetchData()
}

const handleCurrentChange = (page) => {
  groupStore.setPagination(page)
  fetchData()
}

const handleFormSuccess = () => {
  formVisible.value = false
  currentGroup.value = null
  fetchData()
}

const handleMembersSuccess = () => {
  membersDialogVisible.value = false
  fetchData()
}

const handleChangeLeaderSuccess = () => {
  changeLeaderVisible.value = false
  fetchData()
}

// 生命周期
onMounted(() => {
  console.log('🔍 [DEBUG] Group Management - 组件挂载开始')
  console.log('🔍 [DEBUG] groupStore:', groupStore)
  console.log('🔍 [DEBUG] hasPermission function:', hasPermission)
  
  fetchData()
})
</script>

<style scoped>
.group-management {
  padding: 20px;
}

.operation-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 16px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.search-section {
  display: flex;
  align-items: center;
}

.action-section {
  display: flex;
  align-items: center;
  gap: 12px;
}

.group-cards {
  min-height: 400px;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
  padding: 20px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
</style>