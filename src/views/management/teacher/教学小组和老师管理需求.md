# 教学小组和老师管理需求

老师会分成多个教学组，每个教学组有一个组长角色，组长负责添加老师、查看老师课表、给老师排课、停课、移出小组。

## 教学组管理

教学组的管理需要支持以下功能：
1. **创建教学组**：管理员可以创建新的教学组，并指定组长、更换组长、给小组添加和移除老师、删除教学组
2. **添加老师**：组长可以将老师添加到教学组中。
3. **查看组内老师课表**：组长可以查看教学组内所有老师的课表。
4. **排课**：组长可以为教学组内的老师进行排课操作。
5. **停课**：组长可以对教学组内的老师进行停课操作。
6. **移出小组**：组长可以将老师从教学组中移除。

## 老师管理
管理员可以查看所有老师，组长只能查看自己教学组内的老师
老师的管理需要支持以下功能：
1. **查看老师课表**：查看老师课表。
2. **更换教学组**：管理员可以给老师分配到教学组，一个老师只能在一个教学组内。
4. **排课**：组长和管理员有排课权限，可以为教学组内的老师进行排课操作。
5. **停课**：组长和管理员有停课权限，可以对教学组内的老师进行停课操作。可以选择时间范围、停课原因（老师停课、学生停课）和说明。
6. **移出小组**：组长和管理员可以将老师从教学组中移除。
7. **创建老师**：管理员可以创建新的老师账号。