<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="600px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="dialog-content" v-if="group">
      <!-- 当前组长信息 -->
      <div class="current-leader">
        <h4>当前组长</h4>
        <div class="leader-info">
          <el-avatar :size="50" :src="currentLeader?.avatar">
            {{ currentLeader?.name?.charAt(0) }}
          </el-avatar>
          <div class="info-text">
            <div class="name">{{ currentLeader?.name || '未设置' }}</div>
            <div class="meta">
              <span v-if="currentLeader?.phone">{{ currentLeader.phone }}</span>
              <span v-if="currentLeader?.email">{{ currentLeader.email }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 选择新组长 -->
      <div class="new-leader-section">
        <h4>选择新组长</h4>
        <el-form :model="form" label-width="100px">
          <el-form-item label="新组长" required>
            <el-select
              v-model="form.newLeaderId"
              placeholder="请选择新组长"
              filterable
              style="width: 100%"
              :loading="loading"
            >
              <el-option
                v-for="member in groupMembers"
                :key="member.id"
                :label="member.name"
                :value="member.id"
                :disabled="member.id === group.leaderId"
              >
                <div class="member-option">
                  <el-avatar :size="30" :src="member.avatar">
                    {{ member.name?.charAt(0) }}
                  </el-avatar>
                  <div class="member-info">
                    <span class="name">{{ member.name }}</span>
                    <span class="phone">{{ member.phone }}</span>
                  </div>
                  <el-tag
                    v-if="member.id === group.leaderId"
                    type="warning"
                    size="small"
                  >
                    当前组长
                  </el-tag>
                </div>
              </el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="备注">
            <el-input
              v-model="form.remark"
              type="textarea"
              :rows="3"
              placeholder="请输入更换原因或备注（可选）"
            />
          </el-form-item>
        </el-form>
      </div>

      <!-- 提示信息 -->
      <el-alert
        title="注意事项"
        type="warning"
        :closable="false"
        show-icon
      >
        <template #default>
          <ul class="notice-list">
            <li>更换组长后，原组长将变为普通成员</li>
            <li>新组长将获得教学组的管理权限</li>
            <li>此操作不可撤销，请谨慎操作</li>
          </ul>
        </template>
      </el-alert>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          type="primary"
          @click="handleChangeLeader"
          :loading="submitLoading"
          :disabled="!form.newLeaderId"
        >
          确认更换
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch, reactive } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useTeacherGroupStore } from '@/stores/teacherGroup'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  group: {
    type: Object,
    default: null
  }
})

const emit = defineEmits(['update:visible', 'success'])

// 状态管理
const groupStore = useTeacherGroupStore()

// 响应式数据
const loading = ref(false)
const submitLoading = ref(false)
const groupMembers = ref([])
const form = reactive({
  newLeaderId: '',
  remark: ''
})

// 计算属性
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

const dialogTitle = computed(() => {
  const groupName = props.group?.name || '教学组'
  return `更换"${groupName}"组长`
})

const currentLeader = computed(() => {
  return groupMembers.value.find(member => member.id === props.group?.leaderId)
})

// 监听对话框开关
watch(() => props.visible, (visible) => {
  if (visible && props.group) {
    fetchGroupMembers()
    resetForm()
  }
})

// 监听教学组变化
watch(() => props.group, (group) => {
  if (group && props.visible) {
    fetchGroupMembers()
    resetForm()
  }
})

// 方法
const fetchGroupMembers = async () => {
  if (!props.group?.id) return

  loading.value = true
  try {
    await groupStore.fetchGroupMembers(props.group.id)
    groupMembers.value = groupStore.groupMembers
  } catch (error) {
    console.error('获取成员列表失败:', error)
    ElMessage.error('获取成员列表失败')
  } finally {
    loading.value = false
  }
}

const resetForm = () => {
  form.newLeaderId = ''
  form.remark = ''
}

const handleChangeLeader = async () => {
  if (!form.newLeaderId) {
    ElMessage.warning('请选择新组长')
    return
  }

  if (form.newLeaderId === props.group?.leaderId) {
    ElMessage.warning('请选择不同的组长')
    return
  }

  try {
    const newLeader = groupMembers.value.find(member => member.id === form.newLeaderId)

    await ElMessageBox.confirm(
      `确定要将"${newLeader?.name}"设为新组长吗？`,
      '确认更换组长',
      {
        confirmButtonText: '确认更换',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    submitLoading.value = true

    await groupStore.changeGroupLeader(props.group.id, form.newLeaderId)

    ElMessage.success('更换组长成功')
    emit('success')
    handleClose()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('更换组长失败:', error)
      ElMessage.error('更换组长失败')
    }
  } finally {
    submitLoading.value = false
  }
}

const handleClose = () => {
  dialogVisible.value = false
  resetForm()
}
</script>

<style scoped>
.dialog-content {
  padding: 20px 0;
}

.current-leader {
  margin-bottom: 30px;
  padding: 20px;
  background: #F8F9FA;
  border-radius: 8px;
  border: 1px solid #EBEEF5;
}

.current-leader h4 {
  margin: 0 0 16px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.leader-info {
  display: flex;
  align-items: center;
  gap: 16px;
}

.info-text .name {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
}

.info-text .meta {
  font-size: 12px;
  color: #909399;
  display: flex;
  gap: 12px;
}

.new-leader-section {
  margin-bottom: 20px;
}

.new-leader-section h4 {
  margin: 0 0 16px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.member-option {
  display: flex;
  align-items: center;
  gap: 12px;
  width: 100%;
}

.member-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.member-info .name {
  font-size: 14px;
  color: #303133;
}

.member-info .phone {
  font-size: 12px;
  color: #909399;
}

.notice-list {
  margin: 0;
  padding-left: 20px;
  color: #E6A23C;
}

.notice-list li {
  margin-bottom: 4px;
  font-size: 14px;
}

.dialog-footer {
  text-align: right;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}
</style>