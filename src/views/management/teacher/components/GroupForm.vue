<template>
  <el-dialog
    v-model="dialogVisible"
    :title="isEdit ? '编辑教学组' : '创建教学组'"
    width="500px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      label-position="right"
    >
      <el-form-item label="组名" prop="name">
        <el-input 
          v-model="formData.name" 
          placeholder="请输入教学组名称"
          maxlength="50"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="专业科目" prop="subject">
        <el-input 
          v-model="formData.subject" 
          placeholder="请输入专业科目（可选）"
          maxlength="30"
        />
      </el-form-item>

      <el-form-item label="组长" prop="leaderId">
        <el-select 
          v-model="formData.leaderId" 
          placeholder="请选择组长"
          style="width: 100%"
          filterable
          remote
          :remote-method="searchTeachers"
          :loading="teacherLoading"
        >
          <el-option
            v-for="teacher in teacherOptions"
            :key="teacher.id"
            :label="`${teacher.name} (${teacher.phone})`"
            :value="teacher.id"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="描述">
        <el-input 
          v-model="formData.description" 
          type="textarea" 
          :rows="3"
          placeholder="请输入教学组描述（可选）"
          maxlength="200"
          show-word-limit
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button 
          type="primary" 
          :loading="submitLoading"
          @click="handleSubmit"
        >
          {{ isEdit ? '更新' : '创建' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  data: {
    type: Object,
    default: null
  }
})

const emit = defineEmits(['update:visible', 'success'])

// 响应式数据
const formRef = ref()
const submitLoading = ref(false)
const teacherLoading = ref(false)
const teacherOptions = ref([])

const formData = reactive({
  name: '',
  subject: '',
  leaderId: '',
  description: ''
})

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '请输入教学组名称', trigger: 'blur' },
    { min: 2, max: 50, message: '组名长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  leaderId: [
    { required: true, message: '请选择组长', trigger: 'change' }
  ]
}

// 计算属性
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

const isEdit = computed(() => !!props.data)

// 方法 - 移动到 watch 之前
const resetForm = () => {
  console.log('resetForm 函数被调用')
  Object.assign(formData, {
    name: '',
    subject: '',
    leaderId: '',
    description: ''
  })
  teacherOptions.value = []
  if (formRef.value) {
    formRef.value.clearValidate()
  }
}

// 监听数据变化
watch(() => props.data, (newData) => {
  console.log('watch 回调执行，newData:', newData)
  if (newData) {
    // 编辑模式
    Object.assign(formData, {
      name: newData.name || '',
      subject: newData.subject || '',
      leaderId: newData.leaderId || '',
      description: newData.description || ''
    })
    
    // 如果有组长信息，添加到选项中
    if (newData.leaderId && newData.leaderName) {
      teacherOptions.value = [{
        id: newData.leaderId,
        name: newData.leaderName,
        phone: '' // 这里可能需要从API获取完整信息
      }]
    }
  } else {
    // 创建模式
    console.log('调用 resetForm')
    resetForm()
  }
}, { immediate: true })

// resetForm 已移动到上面

const searchTeachers = async (query) => {
  if (!query) {
    teacherOptions.value = []
    return
  }
  
  teacherLoading.value = true
  try {
    // 这里应该调用API搜索老师
    // 暂时模拟数据
    await new Promise(resolve => setTimeout(resolve, 300))
    
    teacherOptions.value = [
      { id: '1', name: '张老师', phone: '13800138001' },
      { id: '2', name: '李老师', phone: '13800138002' },
      { id: '3', name: '王老师', phone: '13800138003' }
    ].filter(teacher => 
      teacher.name.includes(query) || teacher.phone.includes(query)
    )
  } catch (error) {
    console.error('搜索老师失败:', error)
    ElMessage.error('搜索老师失败')
  } finally {
    teacherLoading.value = false
  }
}

const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    
    submitLoading.value = true
    
    // 构建提交数据
    const submitData = {
      name: formData.name,
      subject: formData.subject || undefined,
      leaderId: formData.leaderId,
      description: formData.description || undefined
    }
    
    // 这里应该调用API提交数据
    // 暂时模拟提交成功
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    ElMessage.success(isEdit.value ? '更新教学组成功' : '创建教学组成功')
    emit('success', submitData)
    
  } catch (error) {
    console.error('表单验证失败:', error)
    if (error.message) {
      ElMessage.error(error.message)
    }
  } finally {
    submitLoading.value = false
  }
}

const handleClose = () => {
  dialogVisible.value = false
  resetForm()
}
</script>

<style scoped>
.dialog-footer {
  text-align: right;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-input__count) {
  color: #909399;
}

:deep(.el-textarea__inner) {
  resize: vertical;
}
</style>