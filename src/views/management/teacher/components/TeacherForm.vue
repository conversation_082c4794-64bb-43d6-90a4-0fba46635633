<template>
  <el-dialog
    v-model="dialogVisible"
    :title="isEdit ? '编辑老师' : '创建老师'"
    width="600px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      label-position="right"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="姓名" prop="name">
            <el-input 
              v-model="formData.name" 
              placeholder="请输入姓名"
              maxlength="20"
              show-word-limit
            />
          </el-form-item>
        </el-col>
        
        <el-col :span="12">
          <el-form-item label="手机号" prop="phone">
            <el-input 
              v-model="formData.phone" 
              placeholder="请输入手机号"
              maxlength="11"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="邮箱" prop="email">
            <el-input 
              v-model="formData.email" 
              placeholder="请输入邮箱（可选）"
              type="email"
            />
          </el-form-item>
        </el-col>
        
        <el-col :span="12">
          <el-form-item label="角色" prop="role">
            <el-select 
              v-model="formData.role" 
              placeholder="请选择角色"
              style="width: 100%"
            >
              <el-option label="老师" value="teacher" />
              <el-option 
                label="组长" 
                value="group_leader" 
                v-if="canCreateGroupLeader"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="教学组" prop="groupId">
        <el-select 
          v-model="formData.groupId" 
          placeholder="请选择教学组（可选）"
          style="width: 100%"
          clearable
        >
          <el-option
            v-for="group in groupOptions"
            :key="group.value"
            :label="group.label"
            :value="group.value"
            :disabled="group.disabled"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="专业科目">
        <div class="subjects-input">
          <el-tag
            v-for="subject in formData.subjects"
            :key="subject"
            closable
            @close="removeSubject(subject)"
            style="margin-right: 8px; margin-bottom: 8px;"
          >
            {{ subject }}
          </el-tag>
          
          <el-input
            v-if="inputVisible"
            ref="inputRef"
            v-model="inputValue"
            size="small"
            style="width: 120px; margin-right: 8px;"
            @keyup.enter="handleInputConfirm"
            @blur="handleInputConfirm"
          />
          
          <el-button 
            v-else 
            class="button-new-tag" 
            size="small" 
            @click="showInput"
          >
            + 添加科目
          </el-button>
        </div>
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button 
          type="primary" 
          :loading="submitLoading"
          @click="handleSubmit"
        >
          {{ isEdit ? '更新' : '创建' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { useTeacherPermissions } from '../composables/useTeacherPermissions'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  data: {
    type: Object,
    default: null
  },
  groupOptions: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['update:visible', 'success'])

// 权限控制
const { hasPermission } = useTeacherPermissions()

// 响应式数据
const formRef = ref()
const inputRef = ref()
const submitLoading = ref(false)
const inputVisible = ref(false)
const inputValue = ref('')

const formData = reactive({
  name: '',
  phone: '',
  email: '',
  role: 'teacher',
  groupId: '',
  subjects: []
})

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '请输入姓名', trigger: 'blur' },
    { min: 2, max: 20, message: '姓名长度在 2 到 20 个字符', trigger: 'blur' }
  ],
  phone: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号格式', trigger: 'blur' }
  ],
  email: [
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  role: [
    { required: true, message: '请选择角色', trigger: 'change' }
  ]
}

// 计算属性
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

const isEdit = computed(() => !!props.data)

const canCreateGroupLeader = computed(() => 
  hasPermission('management:teacher:create:group_leader')
)

// 方法 - 移动到 watch 之前
const resetForm = () => {
  console.log('TeacherForm resetForm 函数被调用')
  Object.assign(formData, {
    name: '',
    phone: '',
    email: '',
    role: 'teacher',
    groupId: '',
    subjects: []
  })
  if (formRef.value) {
    formRef.value.clearValidate()
  }
}

const removeSubject = (subject) => {
  const index = formData.subjects.indexOf(subject)
  if (index > -1) {
    formData.subjects.splice(index, 1)
  }
}

const showInput = () => {
  inputVisible.value = true
  nextTick(() => {
    inputRef.value?.focus()
  })
}

const handleInputConfirm = () => {
  const value = inputValue.value.trim()
  if (value && !formData.subjects.includes(value)) {
    formData.subjects.push(value)
  }
  inputVisible.value = false
  inputValue.value = ''
}

const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    
    submitLoading.value = true
    
    // 构建提交数据
    const submitData = {
      name: formData.name,
      phone: formData.phone,
      email: formData.email || undefined,
      role: formData.role,
      groupId: formData.groupId || undefined,
      subjects: formData.subjects.length > 0 ? formData.subjects : undefined
    }
    
    // 这里应该调用API提交数据
    // 暂时模拟提交成功
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    ElMessage.success(isEdit.value ? '更新老师信息成功' : '创建老师成功')
    emit('success', submitData)
    
  } catch (error) {
    console.error('表单验证失败:', error)
    if (error.message) {
      ElMessage.error(error.message)
    }
  } finally {
    submitLoading.value = false
  }
}

const handleClose = () => {
  dialogVisible.value = false
  resetForm()
}

// 监听数据变化
watch(() => props.data, (newData) => {
  console.log('TeacherForm watch 回调执行，newData:', newData)
  if (newData) {
    // 编辑模式
    Object.assign(formData, {
      name: newData.name || '',
      phone: newData.phone || '',
      email: newData.email || '',
      role: newData.role || 'teacher',
      groupId: newData.groupId || '',
      subjects: [...(newData.subjects || [])]
    })
  } else {
    // 创建模式
    console.log('TeacherForm 调用 resetForm')
    resetForm()
  }
}, { immediate: true })
</script>

<style scoped>
.subjects-input {
  min-height: 32px;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

.button-new-tag {
  border-style: dashed;
}

.dialog-footer {
  text-align: right;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-input__count) {
  color: #909399;
}
</style>