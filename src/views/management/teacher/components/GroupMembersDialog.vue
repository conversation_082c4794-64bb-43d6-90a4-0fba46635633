<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="900px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="members-content" v-if="group">
      <!-- 操作栏 -->
      <div class="operation-bar">
        <div class="info-section">
          <el-tag type="info">当前成员：{{ members.length }}人</el-tag>
          <el-tag v-if="group.leaderName" type="warning">组长：{{ group.leaderName }}</el-tag>
        </div>

        <div class="action-section">
          <el-button
            type="primary"
            size="small"
            @click="showAddMemberDialog"
            v-if="canManageGroup(group.id)"
          >
            <el-icon><Plus /></el-icon>
            添加成员
          </el-button>
        </div>
      </div>

      <!-- 成员列表 -->
      <div class="members-list">
        <el-table
          :data="members"
          :loading="loading"
          row-key="id"
          style="width: 100%"
        >
          <!-- 头像 -->
          <el-table-column label="头像" width="80">
            <template #default="{ row }">
              <el-avatar :size="40" :src="row.avatar">
                {{ row.name.charAt(0) }}
              </el-avatar>
            </template>
          </el-table-column>

          <!-- 姓名 -->
          <el-table-column prop="name" label="姓名" min-width="120">
            <template #default="{ row }">
              <div class="name-cell">
                <span class="name">{{ row.name }}</span>
                <el-tag
                  v-if="row.id === group.leaderId"
                  type="warning"
                  size="small"
                >
                  组长
                </el-tag>
              </div>
            </template>
          </el-table-column>

          <!-- 手机号 -->
          <el-table-column prop="phone" label="手机号" width="130" />

          <!-- 邮箱 -->
          <el-table-column prop="email" label="邮箱" min-width="150">
            <template #default="{ row }">
              {{ row.email || '-' }}
            </template>
          </el-table-column>

          <!-- 专业科目 -->
          <el-table-column label="专业科目" min-width="120">
            <template #default="{ row }">
              <div v-if="row.subjects && row.subjects.length">
                <el-tag
                  v-for="subject in row.subjects.slice(0, 2)"
                  :key="subject"
                  size="small"
                  style="margin-right: 4px; margin-bottom: 4px;"
                >
                  {{ subject }}
                </el-tag>
                <el-text
                  v-if="row.subjects.length > 2"
                  type="info"
                  size="small"
                >
                  +{{ row.subjects.length - 2 }}
                </el-text>
              </div>
              <span v-else>-</span>
            </template>
          </el-table-column>

          <!-- 加入时间 -->
          <el-table-column label="加入时间" width="110">
            <template #default="{ row }">
              {{ formatDate(row.joinedAt || row.createdAt) }}
            </template>
          </el-table-column>

          <!-- 操作 -->
          <el-table-column label="操作" width="120" fixed="right">
            <template #default="{ row }">
              <div class="action-buttons">
                <!-- 移出组 -->
                <el-button
                  type="danger"
                  size="small"
                  text
                  @click="handleRemoveMember(row)"
                  v-if="canRemoveMember(row)"
                >
                  <el-icon><Remove /></el-icon>
                  移出
                </el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 空状态 -->
      <el-empty
        v-if="!loading && members.length === 0"
        :image-size="120"
        description="暂无成员"
      >
        <el-button
          type="primary"
          @click="showAddMemberDialog"
          v-if="canManageGroup(group.id)"
        >
          添加第一个成员
        </el-button>
      </el-empty>
    </div>

    <!-- 添加成员弹窗 -->
    <el-dialog
      v-model="addMemberVisible"
      title="添加成员"
      width="600px"
      :close-on-click-modal="false"
      append-to-body
    >
      <div class="add-member-content">
        <el-form :model="addMemberForm" label-width="80px">
          <el-form-item label="选择老师">
            <el-select
              v-model="addMemberForm.teacherIds"
              placeholder="请选择要添加的老师"
              multiple
              filterable
              style="width: 100%"
            >
              <el-option
                v-for="teacher in availableTeachers"
                :key="teacher.id"
                :label="teacher.name"
                :value="teacher.id"
              >
                <div class="teacher-option">
                  <span>{{ teacher.name }}</span>
                  <span class="phone">{{ teacher.phone }}</span>
                </div>
              </el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="addMemberVisible = false">取消</el-button>
          <el-button
            type="primary"
            @click="handleAddMembers"
            :loading="addMemberLoading"
          >
            确定添加
          </el-button>
        </div>
      </template>
    </el-dialog>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch, reactive } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Remove } from '@element-plus/icons-vue'
import { useTeacherGroupStore } from '@/stores/teacherGroup'
import { useTeacherManagementStore } from '@/stores/teacherManagement'
import { useTeacherPermissions } from '../composables/useTeacherPermissions'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  group: {
    type: Object,
    default: null
  }
})

const emit = defineEmits(['update:visible', 'success'])

// 状态管理
const groupStore = useTeacherGroupStore()
const teacherStore = useTeacherManagementStore()
const { canManageGroup } = useTeacherPermissions()

// 响应式数据
const loading = ref(false)
const members = ref([])
const addMemberVisible = ref(false)
const addMemberLoading = ref(false)
const addMemberForm = reactive({
  teacherIds: []
})

// 计算属性
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

const dialogTitle = computed(() => {
  const groupName = props.group?.name || '教学组'
  return `管理"${groupName}"成员`
})

// 可添加的老师（未分配到任何组或不在当前组的老师）
const availableTeachers = computed(() => {
  return teacherStore.teachers.filter(teacher =>
    !teacher.groupId || teacher.groupId !== props.group?.id
  )
})

// 监听对话框开关
watch(() => props.visible, (visible) => {
  if (visible && props.group) {
    fetchMembers()
    fetchAvailableTeachers()
  }
})

// 监听教学组变化
watch(() => props.group, (group) => {
  if (group && props.visible) {
    fetchMembers()
    fetchAvailableTeachers()
  }
})

// 方法
const fetchMembers = async () => {
  if (!props.group?.id) return

  loading.value = true
  try {
    await groupStore.fetchGroupMembers(props.group.id)
    members.value = groupStore.groupMembers
  } catch (error) {
    console.error('获取成员列表失败:', error)
    ElMessage.error('获取成员列表失败')
  } finally {
    loading.value = false
  }
}

const fetchAvailableTeachers = async () => {
  try {
    await teacherStore.fetchTeachers({ status: 'active' })
  } catch (error) {
    console.error('获取可用老师列表失败:', error)
  }
}

const showAddMemberDialog = () => {
  addMemberForm.teacherIds = []
  addMemberVisible.value = true
}

const handleAddMembers = async () => {
  if (addMemberForm.teacherIds.length === 0) {
    ElMessage.warning('请选择要添加的老师')
    return
  }

  addMemberLoading.value = true
  try {
    let successCount = 0
    let failCount = 0

    for (const teacherId of addMemberForm.teacherIds) {
      try {
        await groupStore.addTeacherToGroup(props.group.id, teacherId)
        successCount++
      } catch (error) {
        console.error(`添加老师 ${teacherId} 失败:`, error)
        failCount++
      }
    }

    if (successCount > 0) {
      ElMessage.success(`成功添加 ${successCount} 位老师${failCount > 0 ? `，失败 ${failCount} 位` : ''}`)
      addMemberVisible.value = false
      await fetchMembers()
      emit('success')
    } else {
      ElMessage.error('添加成员失败')
    }
  } catch (error) {
    console.error('批量添加成员失败:', error)
    ElMessage.error('添加成员失败')
  } finally {
    addMemberLoading.value = false
  }
}

const handleRemoveMember = async (member) => {
  try {
    await ElMessageBox.confirm(
      `确定要将"${member.name}"从教学组中移除吗？`,
      '确认移除',
      {
        confirmButtonText: '移除',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await groupStore.removeTeacherFromGroup(props.group.id, member.id)
    await fetchMembers()
    emit('success')
  } catch (error) {
    if (error !== 'cancel') {
      console.error('移除成员失败:', error)
    }
  }
}

const canRemoveMember = (member) => {
  // 不能移除组长
  if (member.id === props.group?.leaderId) return false

  // 检查是否有管理权限
  return canManageGroup(props.group?.id)
}

const handleClose = () => {
  dialogVisible.value = false
}

const formatDate = (dateString) => {
  if (!dateString) return '-'
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN')
}
</script>

<style scoped>
.members-content {
  min-height: 400px;
}

.operation-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 16px;
  background: #F8F9FA;
  border-radius: 8px;
}

.info-section {
  display: flex;
  align-items: center;
  gap: 12px;
}

.action-section {
  display: flex;
  align-items: center;
  gap: 12px;
}

.members-list {
  background: #fff;
  border-radius: 8px;
  border: 1px solid #EBEEF5;
}

.name-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.name {
  font-weight: 500;
}

.action-buttons {
  display: flex;
  align-items: center;
  gap: 4px;
}

.add-member-content {
  padding: 20px 0;
}

.teacher-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.teacher-option .phone {
  color: #909399;
  font-size: 12px;
}

.dialog-footer {
  text-align: right;
}

:deep(.el-table__cell) {
  padding: 12px 0;
}

:deep(.el-button + .el-button) {
  margin-left: 4px;
}
</style>