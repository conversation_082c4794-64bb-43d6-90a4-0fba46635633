<template>
  <el-dialog
    v-model="dialogVisible"
    :title="`${teacher?.name || '老师'}的课表`"
    width="90%"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="schedule-content" v-if="teacher">
      <!-- 课表头部操作栏 -->
      <div class="schedule-header">
        <div class="teacher-info">
          <el-avatar :size="40" :src="teacher.avatar">
            {{ teacher.name?.charAt(0) }}
          </el-avatar>
          <div class="info-text">
            <div class="name">{{ teacher.name }}</div>
            <div class="meta">
              <span v-if="teacher.groupName">{{ teacher.groupName }}</span>
              <span v-if="teacher.subjects?.length" class="subjects">
                {{ teacher.subjects.join('、') }}
              </span>
            </div>
          </div>
        </div>

        <div class="schedule-controls">
          <!-- 视图切换 -->
          <el-radio-group v-model="currentView" size="small">
            <el-radio-button label="week">周视图</el-radio-button>
            <el-radio-button label="month">月视图</el-radio-button>
          </el-radio-group>

          <!-- 日期选择 -->
          <el-date-picker
            v-model="selectedDate"
            type="date"
            placeholder="选择日期"
            size="small"
            style="margin-left: 12px; width: 140px;"
            @change="handleDateChange"
          />

          <!-- 排课按钮 -->
          <el-button 
            type="primary" 
            size="small"
            @click="handleSchedule"
            v-if="canScheduleForTeacher(teacher)"
            style="margin-left: 12px;"
          >
            <el-icon><Plus /></el-icon>
            排课
          </el-button>

          <!-- 刷新按钮 -->
          <el-button 
            size="small"
            @click="handleRefresh"
            style="margin-left: 8px;"
          >
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
        </div>
      </div>

      <!-- 课表内容区域 -->
      <div class="schedule-wrapper">
        <el-loading :loading="loading" element-loading-text="加载课表中...">
          <!-- 周视图 -->
          <WeekView 
            v-if="currentView === 'week'"
            :schedules="schedules"
            :selected-date="selectedDate"
            :teacher-id="teacher.id"
            @course-action="handleCourseAction"
            @date-change="handleDateChange"
          />
          
          <!-- 月视图 -->
          <MonthView 
            v-else
            :schedules="schedules"
            :selected-date="selectedDate"
            :teacher-id="teacher.id"
            @course-action="handleCourseAction"
            @date-change="handleDateChange"
          />
        </el-loading>
      </div>

      <!-- 课程统计信息 -->
      <div class="schedule-stats">
        <el-row :gutter="16">
          <el-col :span="6">
            <el-statistic 
              title="本周课程" 
              :value="weekStats.total"
              suffix="节"
            />
          </el-col>
          <el-col :span="6">
            <el-statistic 
              title="已完成" 
              :value="weekStats.completed"
              suffix="节"
            >
              <template #title>
                <span style="color: #67C23A;">已完成</span>
              </template>
            </el-statistic>
          </el-col>
          <el-col :span="6">
            <el-statistic 
              title="进行中" 
              :value="weekStats.ongoing"
              suffix="节"
            >
              <template #title>
                <span style="color: #409EFF;">进行中</span>
              </template>
            </el-statistic>
          </el-col>
          <el-col :span="6">
            <el-statistic 
              title="待开始" 
              :value="weekStats.pending"
              suffix="节"
            >
              <template #title>
                <span style="color: #E6A23C;">待开始</span>
              </template>
            </el-statistic>
          </el-col>
        </el-row>
      </div>
    </div>

    <!-- 排课弹窗 -->
    <ScheduleDialog 
      v-model:visible="scheduleDialogVisible"
      :teacher-id="teacher?.id"
      @success="handleScheduleSuccess"
    />
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Plus, Refresh } from '@element-plus/icons-vue'
import { useTeacherPermissions } from '../composables/useTeacherPermissions'
import { useCurriculumStore } from '@/stores/curriculum'
// 复用现有的课表组件
import WeekView from '@/views/curriculum/components/WeekView.vue'
import MonthView from '@/views/curriculum/components/MonthView.vue'
import ScheduleDialog from '@/views/curriculum/components/ScheduleDialog.vue'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  teacher: {
    type: Object,
    default: null
  }
})

const emit = defineEmits(['update:visible'])

// 权限控制
const { canScheduleForTeacher } = useTeacherPermissions()

// 课表store
const curriculumStore = useCurriculumStore()

// 响应式数据
const currentView = ref('week')
const selectedDate = ref(new Date())
const scheduleDialogVisible = ref(false)
const loading = ref(false)

// 计算属性
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

const schedules = computed(() => curriculumStore.schedules || [])

// 课程统计
const weekStats = computed(() => {
  const stats = {
    total: 0,
    completed: 0,
    ongoing: 0,
    pending: 0,
    cancelled: 0
  }
  
  schedules.value.forEach(schedule => {
    stats.total++
    stats[schedule.status]++
  })
  
  return stats
})

// 监听对话框开关
watch(() => props.visible, (visible) => {
  if (visible && props.teacher) {
    fetchSchedules()
  }
})

// 监听老师变化
watch(() => props.teacher, (teacher) => {
  if (teacher && props.visible) {
    fetchSchedules()
  }
})

// 方法
const fetchSchedules = async () => {
  if (!props.teacher?.id) return
  
  loading.value = true
  try {
    const startDate = getWeekStartDate(selectedDate.value)
    const endDate = getWeekEndDate(selectedDate.value)
    
    await curriculumStore.fetchSchedules({
      teacherId: props.teacher.id,
      startDate: formatDate(startDate),
      endDate: formatDate(endDate),
      viewType: currentView.value
    })
  } catch (error) {
    console.error('获取课表失败:', error)
    ElMessage.error('获取课表失败')
  } finally {
    loading.value = false
  }
}

const handleDateChange = (date) => {
  selectedDate.value = date
  fetchSchedules()
}

const handleRefresh = () => {
  fetchSchedules()
}

const handleSchedule = () => {
  scheduleDialogVisible.value = true
}

const handleScheduleSuccess = () => {
  scheduleDialogVisible.value = false
  fetchSchedules()
  ElMessage.success('排课成功')
}

const handleCourseAction = async (action) => {
  try {
    switch (action.type) {
      case 'start':
        await curriculumStore.startCourse(action.courseId)
        ElMessage.success('开始上课')
        break
      case 'cancel':
        await curriculumStore.cancelCourse(action.courseId, action.data)
        ElMessage.success('停课成功')
        break
      case 'end':
        await curriculumStore.endCourse(action.courseId)
        ElMessage.success('结束上课')
        break
    }
    fetchSchedules()
  } catch (error) {
    console.error('操作失败:', error)
    ElMessage.error('操作失败')
  }
}

const handleClose = () => {
  dialogVisible.value = false
}

// 工具函数
const getWeekStartDate = (date) => {
  const d = new Date(date)
  const day = d.getDay()
  const diff = d.getDate() - day + (day === 0 ? -6 : 1) // 周一为一周开始
  return new Date(d.setDate(diff))
}

const getWeekEndDate = (date) => {
  const start = getWeekStartDate(date)
  return new Date(start.getTime() + 6 * 24 * 60 * 60 * 1000)
}

const formatDate = (date) => {
  return date.toISOString().split('T')[0]
}

// 生命周期
onMounted(() => {
  if (props.visible && props.teacher) {
    fetchSchedules()
  }
})
</script>

<style scoped>
.schedule-content {
  min-height: 600px;
}

.schedule-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 0;
  border-bottom: 1px solid #EBEEF5;
  margin-bottom: 20px;
}

.teacher-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.info-text .name {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
}

.info-text .meta {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.subjects {
  margin-left: 8px;
  padding-left: 8px;
  border-left: 1px solid #E4E7ED;
}

.schedule-controls {
  display: flex;
  align-items: center;
}

.schedule-wrapper {
  min-height: 400px;
  background: #fff;
  border-radius: 8px;
  border: 1px solid #EBEEF5;
  margin-bottom: 20px;
}

.schedule-stats {
  padding: 20px;
  background: #F8F9FA;
  border-radius: 8px;
  border: 1px solid #EBEEF5;
}

:deep(.el-statistic__content) {
  display: flex;
  align-items: center;
  justify-content: center;
}

:deep(.el-statistic__number) {
  font-size: 24px;
  font-weight: 600;
}
</style>