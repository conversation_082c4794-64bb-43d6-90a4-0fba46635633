<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="90%"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="schedule-content" v-if="group">
      <el-alert
        title="功能开发中"
        type="info"
        description="教学组课表查看功能正在开发中，敬请期待..."
        :closable="false"
        show-icon
      />
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  group: {
    type: Object,
    default: null
  }
})

const emit = defineEmits(['update:visible'])

const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

const dialogTitle = computed(() => {
  const groupName = props.group?.name || '教学组'
  return `"${groupName}"课表`
})

const handleClose = () => {
  dialogVisible.value = false
}
</script>

<style scoped>
.schedule-content {
  padding: 20px 0;
  min-height: 300px;
}

.dialog-footer {
  text-align: right;
}
</style>