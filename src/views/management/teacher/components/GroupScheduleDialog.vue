<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="95%"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="schedule-content" v-if="group">
      <!-- 教学组信息头部 -->
      <div class="group-header">
        <div class="group-info">
          <div class="group-name">{{ group.name }}</div>
          <div class="group-meta">
            <span class="leader">组长：{{ group.leaderName }}</span>
            <span class="member-count">成员：{{ group.teacherCount }}人</span>
            <span v-if="group.subject" class="subject">科目：{{ group.subject }}</span>
          </div>
        </div>

        <div class="schedule-controls">
          <!-- 视图切换 -->
          <el-radio-group v-model="currentView" size="small">
            <el-radio-button label="week">周视图</el-radio-button>
            <el-radio-button label="month">月视图</el-radio-button>
          </el-radio-group>

          <!-- 日期选择 -->
          <el-date-picker
            v-model="selectedDate"
            type="date"
            placeholder="选择日期"
            size="small"
            style="margin-left: 12px; width: 140px;"
            @change="handleDateChange"
          />

          <!-- 老师筛选 -->
          <el-select
            v-model="selectedTeacherId"
            placeholder="选择老师"
            size="small"
            clearable
            style="margin-left: 12px; width: 150px;"
            @change="handleTeacherChange"
          >
            <el-option label="全部老师" value="" />
            <el-option
              v-for="teacher in groupMembers"
              :key="teacher.id"
              :label="teacher.name"
              :value="teacher.id"
            />
          </el-select>

          <!-- 刷新按钮 -->
          <el-button
            size="small"
            @click="handleRefresh"
            style="margin-left: 8px;"
          >
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
        </div>
      </div>

      <!-- 课表内容区域 -->
      <div class="schedule-wrapper">
        <el-loading :loading="loading" element-loading-text="加载课表中...">
          <!-- 周视图 -->
          <WeekView
            v-if="currentView === 'week'"
            :schedules="filteredSchedules"
            :selected-date="selectedDate"
            :teacher-id="selectedTeacherId"
            @course-action="handleCourseAction"
            @date-change="handleDateChange"
          />

          <!-- 月视图 -->
          <MonthView
            v-else
            :schedules="filteredSchedules"
            :selected-date="selectedDate"
            :teacher-id="selectedTeacherId"
            @course-action="handleCourseAction"
            @date-change="handleDateChange"
          />
        </el-loading>
      </div>

      <!-- 统计信息 -->
      <div class="schedule-stats">
        <el-row :gutter="16">
          <el-col :span="4">
            <el-statistic
              title="总课程"
              :value="groupStats.total"
              suffix="节"
            />
          </el-col>
          <el-col :span="4">
            <el-statistic
              title="已完成"
              :value="groupStats.completed"
              suffix="节"
            >
              <template #title>
                <span style="color: #67C23A;">已完成</span>
              </template>
            </el-statistic>
          </el-col>
          <el-col :span="4">
            <el-statistic
              title="进行中"
              :value="groupStats.ongoing"
              suffix="节"
            >
              <template #title>
                <span style="color: #409EFF;">进行中</span>
              </template>
            </el-statistic>
          </el-col>
          <el-col :span="4">
            <el-statistic
              title="待开始"
              :value="groupStats.pending"
              suffix="节"
            >
              <template #title>
                <span style="color: #E6A23C;">待开始</span>
              </template>
            </el-statistic>
          </el-col>
          <el-col :span="4">
            <el-statistic
              title="已取消"
              :value="groupStats.cancelled"
              suffix="节"
            >
              <template #title>
                <span style="color: #F56C6C;">已取消</span>
              </template>
            </el-statistic>
          </el-col>
          <el-col :span="4">
            <el-statistic
              title="活跃老师"
              :value="activeTeachersCount"
              suffix="人"
            />
          </el-col>
        </el-row>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Refresh } from '@element-plus/icons-vue'
import { useTeacherGroupStore } from '@/stores/teacherGroup'
import { useCurriculumStore } from '@/stores/curriculum'
// 复用现有的课表组件
import WeekView from '@/views/curriculum/components/WeekView.vue'
import MonthView from '@/views/curriculum/components/MonthView.vue'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  group: {
    type: Object,
    default: null
  }
})

const emit = defineEmits(['update:visible'])

// 状态管理
const groupStore = useTeacherGroupStore()
const curriculumStore = useCurriculumStore()

// 响应式数据
const currentView = ref('week')
const selectedDate = ref(new Date())
const selectedTeacherId = ref('')
const loading = ref(false)
const groupMembers = ref([])

// 计算属性
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

const dialogTitle = computed(() => {
  const groupName = props.group?.name || '教学组'
  return `"${groupName}"课表`
})

const schedules = computed(() => curriculumStore.schedules || [])

// 根据选中的老师筛选课表
const filteredSchedules = computed(() => {
  if (!selectedTeacherId.value) {
    return schedules.value
  }
  return schedules.value.filter(schedule => schedule.teacherId === selectedTeacherId.value)
})

// 教学组课程统计
const groupStats = computed(() => {
  const stats = {
    total: 0,
    completed: 0,
    ongoing: 0,
    pending: 0,
    cancelled: 0
  }

  schedules.value.forEach(schedule => {
    stats.total++
    if (schedule.status) {
      stats[schedule.status]++
    }
  })

  return stats
})

// 活跃老师数量（有课程的老师）
const activeTeachersCount = computed(() => {
  const teacherIds = new Set(schedules.value.map(s => s.teacherId))
  return teacherIds.size
})

// 监听对话框开关
watch(() => props.visible, (visible) => {
  if (visible && props.group) {
    fetchGroupMembers()
    fetchGroupSchedules()
  }
})

// 监听教学组变化
watch(() => props.group, (group) => {
  if (group && props.visible) {
    fetchGroupMembers()
    fetchGroupSchedules()
  }
})

// 方法
const fetchGroupMembers = async () => {
  if (!props.group?.id) return

  try {
    await groupStore.fetchGroupMembers(props.group.id)
    groupMembers.value = groupStore.groupMembers
  } catch (error) {
    console.error('获取教学组成员失败:', error)
    ElMessage.error('获取教学组成员失败')
  }
}

const fetchGroupSchedules = async () => {
  if (!props.group?.id) return

  loading.value = true
  try {
    const startDate = getWeekStartDate(selectedDate.value)
    const endDate = getWeekEndDate(selectedDate.value)

    // 获取教学组所有成员的课表
    const memberIds = groupMembers.value.map(member => member.id)

    if (memberIds.length > 0) {
      // 由于当前API不支持teacherIds数组，我们需要分别获取每个老师的课表然后合并
      const allSchedules = []

      for (const teacherId of memberIds) {
        try {
          // 获取单个老师的课表
          await curriculumStore.fetchSchedules({
            teacherId: teacherId,
            startDate: formatDate(startDate),
            endDate: formatDate(endDate),
            viewType: currentView.value
          })

          // 获取当前老师的课表数据
          const teacherSchedules = curriculumStore.schedules || []
          allSchedules.push(...teacherSchedules)
        } catch (error) {
          console.error(`获取老师 ${teacherId} 的课表失败:`, error)
        }
      }

      // 手动设置合并后的课表数据
      curriculumStore.schedules = allSchedules
    }
  } catch (error) {
    console.error('获取教学组课表失败:', error)
    ElMessage.error('获取教学组课表失败')
  } finally {
    loading.value = false
  }
}

const handleDateChange = (date) => {
  selectedDate.value = date
  fetchGroupSchedules()
}

const handleTeacherChange = () => {
  // 筛选逻辑在计算属性中处理
}

const handleRefresh = () => {
  fetchGroupSchedules()
}

const handleCourseAction = async (action) => {
  try {
    switch (action.type) {
      case 'start':
        await curriculumStore.startCourse(action.courseId)
        ElMessage.success('开始上课')
        break
      case 'cancel':
        await curriculumStore.cancelCourse(action.courseId, action.data)
        ElMessage.success('停课成功')
        break
      case 'end':
        await curriculumStore.endCourse(action.courseId)
        ElMessage.success('结束上课')
        break
    }
    fetchGroupSchedules()
  } catch (error) {
    console.error('操作失败:', error)
    ElMessage.error('操作失败')
  }
}

const handleClose = () => {
  dialogVisible.value = false
  // 清空选择状态
  selectedTeacherId.value = ''
}

// 工具函数
const getWeekStartDate = (date) => {
  const d = new Date(date)
  const day = d.getDay()
  const diff = d.getDate() - day + (day === 0 ? -6 : 1) // 周一为一周开始
  return new Date(d.setDate(diff))
}

const getWeekEndDate = (date) => {
  const start = getWeekStartDate(date)
  return new Date(start.getTime() + 6 * 24 * 60 * 60 * 1000)
}

const formatDate = (date) => {
  return date.toISOString().split('T')[0]
}

// 生命周期
onMounted(() => {
  if (props.visible && props.group) {
    fetchGroupMembers()
    fetchGroupSchedules()
  }
})
</script>

<style scoped>
.schedule-content {
  min-height: 600px;
}

.group-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 0;
  border-bottom: 1px solid #EBEEF5;
  margin-bottom: 20px;
}

.group-info .group-name {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 8px;
}

.group-meta {
  display: flex;
  align-items: center;
  gap: 16px;
  font-size: 14px;
  color: #606266;
}

.group-meta .leader {
  color: #E6A23C;
  font-weight: 500;
}

.group-meta .member-count {
  color: #409EFF;
}

.group-meta .subject {
  color: #67C23A;
}

.schedule-controls {
  display: flex;
  align-items: center;
}

.schedule-wrapper {
  min-height: 400px;
  background: #fff;
  border-radius: 8px;
  border: 1px solid #EBEEF5;
  margin-bottom: 20px;
}

.schedule-stats {
  padding: 20px;
  background: #F8F9FA;
  border-radius: 8px;
  border: 1px solid #EBEEF5;
}

.dialog-footer {
  text-align: right;
}

:deep(.el-statistic__content) {
  display: flex;
  align-items: center;
  justify-content: center;
}

:deep(.el-statistic__number) {
  font-size: 20px;
  font-weight: 600;
}

:deep(.el-statistic__title) {
  font-size: 12px;
  margin-bottom: 8px;
}
</style>