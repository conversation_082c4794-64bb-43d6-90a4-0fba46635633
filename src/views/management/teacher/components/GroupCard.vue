<template>
  <el-card class="group-card" :class="{ 'inactive': group.status === 'inactive' }">
    <template #header>
      <div class="card-header">
        <div class="group-info">
          <h3 class="group-name">{{ group.name }}</h3>
          <el-tag 
            :type="group.status === 'active' ? 'success' : 'info'"
            size="small"
          >
            {{ group.status === 'active' ? '启用' : '禁用' }}
          </el-tag>
        </div>
        
        <el-dropdown @command="handleCommand" trigger="click">
          <el-button type="text" class="more-btn">
            <el-icon><MoreFilled /></el-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item 
                command="edit" 
                v-if="canManageGroup(group.id)"
              >
                <el-icon><Edit /></el-icon>
                编辑信息
              </el-dropdown-item>
              <el-dropdown-item 
                command="members"
                v-if="canManageGroup(group.id)"
              >
                <el-icon><User /></el-icon>
                管理成员
              </el-dropdown-item>
              <el-dropdown-item 
                command="schedule"
                v-if="canViewSchedule"
              >
                <el-icon><Calendar /></el-icon>
                查看课表
              </el-dropdown-item>
              <el-dropdown-item 
                command="change-leader" 
                v-if="canManageGroup(group.id)"
                divided
              >
                <el-icon><User /></el-icon>
                更换组长
              </el-dropdown-item>
              <el-dropdown-item 
                command="delete" 
                v-if="canManageGroup(group.id)"
              >
                <el-icon style="color: #F56C6C;"><Delete /></el-icon>
                <span style="color: #F56C6C;">删除教学组</span>
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </template>

    <div class="card-content">
      <!-- 组长信息 -->
      <div class="leader-section">
        <div class="section-title">
          <el-icon><User /></el-icon>
          <span>组长</span>
        </div>
        <div class="leader-info">
          <el-avatar :size="32" class="leader-avatar">
            {{ group.leaderName?.charAt(0) || 'N' }}
          </el-avatar>
          <span class="leader-name">{{ group.leaderName || '未指定' }}</span>
        </div>
      </div>

      <!-- 统计信息 -->
      <div class="stats-section">
        <div class="stat-item">
          <span class="stat-label">成员数量</span>
          <span class="stat-value">{{ group.teacherCount || 0 }}人</span>
        </div>
        <div class="stat-item" v-if="group.subject">
          <span class="stat-label">专业科目</span>
          <span class="stat-value">{{ group.subject }}</span>
        </div>
      </div>

      <!-- 描述信息 -->
      <div class="description-section" v-if="group.description">
        <div class="section-title">
          <el-icon><Document /></el-icon>
          <span>描述</span>
        </div>
        <p class="description-text">{{ group.description }}</p>
      </div>

      <!-- 创建时间 -->
      <div class="meta-section">
        <el-text type="info" size="small">
          <el-icon><Clock /></el-icon>
          创建于 {{ formatDate(group.createdAt) }}
        </el-text>
      </div>
    </div>

    <!-- 快捷操作按钮 -->
    <template #footer>
      <div class="card-actions">
        <el-button 
          size="small" 
          @click="handleManageMembers"
          v-if="canManageGroup(group.id)"
        >
          <el-icon><User /></el-icon>
          管理成员
        </el-button>
        
        <el-button 
          size="small" 
          type="primary"
          @click="handleViewSchedule"
          v-if="canViewSchedule"
        >
          <el-icon><Calendar /></el-icon>
          查看课表
        </el-button>
      </div>
    </template>
  </el-card>
</template>

<script setup>
import { computed } from 'vue'
import {
  MoreFilled,
  Edit,
  Delete,
  User,
  Calendar,
  Document,
  Clock
} from '@element-plus/icons-vue'
import { useTeacherPermissions } from '../composables/useTeacherPermissions'

const props = defineProps({
  group: {
    type: Object,
    required: true
  }
})

const emit = defineEmits([
  'edit',
  'delete',
  'change-leader',
  'manage-members',
  'view-schedule'
])

// 权限控制
const {
  canManageGroup,
  canViewSchedule: hasViewSchedulePermission
} = useTeacherPermissions()

const canViewSchedule = computed(() => 
  hasViewSchedulePermission() || canManageGroup(props.group.id)
)

// 方法
const handleCommand = (command) => {
  switch (command) {
    case 'edit':
      emit('edit', props.group)
      break
    case 'delete':
      emit('delete', props.group)
      break
    case 'change-leader':
      emit('change-leader', props.group)
      break
    case 'members':
      emit('manage-members', props.group)
      break
    case 'schedule':
      emit('view-schedule', props.group)
      break
  }
}

const handleManageMembers = () => {
  emit('manage-members', props.group)
}

const handleViewSchedule = () => {
  emit('view-schedule', props.group)
}

const formatDate = (dateString) => {
  if (!dateString) return '-'
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN')
}
</script>

<style scoped>
.group-card {
  height: 100%;
  transition: all 0.3s ease;
  border: 1px solid #EBEEF5;
}

.group-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.group-card.inactive {
  opacity: 0.7;
  background-color: #F8F9FA;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.group-info {
  flex: 1;
}

.group-name {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.more-btn {
  padding: 4px;
  color: #909399;
}

.more-btn:hover {
  color: #409EFF;
}

.card-content {
  padding: 0;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
  font-weight: 500;
  color: #606266;
  margin-bottom: 8px;
}

.leader-section {
  margin-bottom: 16px;
}

.leader-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.leader-avatar {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  font-weight: 500;
}

.leader-name {
  font-size: 14px;
  color: #303133;
}

.stats-section {
  margin-bottom: 16px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 6px;
}

.stat-label {
  font-size: 13px;
  color: #909399;
}

.stat-value {
  font-size: 13px;
  color: #303133;
  font-weight: 500;
}

.description-section {
  margin-bottom: 16px;
}

.description-text {
  margin: 0;
  font-size: 13px;
  color: #606266;
  line-height: 1.4;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.meta-section {
  margin-bottom: 8px;
}

.card-actions {
  display: flex;
  gap: 8px;
  justify-content: center;
}

:deep(.el-card__header) {
  padding: 16px 16px 0 16px;
  border-bottom: none;
}

:deep(.el-card__body) {
  padding: 16px;
}

:deep(.el-card__footer) {
  padding: 0 16px 16px 16px;
  border-top: 1px solid #EBEEF5;
  background: #FAFBFC;
}

:deep(.el-dropdown-menu__item) {
  display: flex;
  align-items: center;
  gap: 8px;
}
</style>