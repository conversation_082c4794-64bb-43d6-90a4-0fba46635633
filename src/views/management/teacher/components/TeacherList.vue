<template>
  <div class="teacher-list">
    <el-table
      :data="data"
      :loading="loading"
      row-key="id"
      @selection-change="handleSelectionChange"
      style="width: 100%"
    >
      <!-- 选择列 -->
      <el-table-column type="selection" width="50" />
      
      <!-- 头像 -->
      <el-table-column label="头像" width="80">
        <template #default="{ row }">
          <el-avatar :size="40" :src="row.avatar">
            {{ row.name.charAt(0) }}
          </el-avatar>
        </template>
      </el-table-column>

      <!-- 姓名 -->
      <el-table-column prop="name" label="姓名" min-width="100">
        <template #default="{ row }">
          <div class="name-cell">
            <span class="name">{{ row.name }}</span>
            <el-tag 
              v-if="row.role === 'admin'" 
              type="danger" 
              size="small"
            >
              管理员
            </el-tag>
            <el-tag 
              v-else-if="row.role === 'group_leader'" 
              type="warning" 
              size="small"
            >
              组长
            </el-tag>
          </div>
        </template>
      </el-table-column>

      <!-- 手机号 -->
      <el-table-column prop="phone" label="手机号" width="120" />

      <!-- 邮箱 -->
      <el-table-column prop="email" label="邮箱" min-width="150">
        <template #default="{ row }">
          {{ row.email || '-' }}
        </template>
      </el-table-column>

      <!-- 教学组 -->
      <el-table-column label="教学组" min-width="120">
        <template #default="{ row }">
          <div v-if="row.groupName" class="group-cell">
            <el-tag type="info">{{ row.groupName }}</el-tag>
          </div>
          <span v-else class="no-group">未分配</span>
        </template>
      </el-table-column>

      <!-- 专业科目 -->
      <el-table-column label="专业科目" min-width="120">
        <template #default="{ row }">
          <div v-if="row.subjects && row.subjects.length">
            <el-tag 
              v-for="subject in row.subjects.slice(0, 2)" 
              :key="subject"
              size="small"
              style="margin-right: 4px; margin-bottom: 4px;"
            >
              {{ subject }}
            </el-tag>
            <el-text 
              v-if="row.subjects.length > 2" 
              type="info" 
              size="small"
            >
              +{{ row.subjects.length - 2 }}
            </el-text>
          </div>
          <span v-else>-</span>
        </template>
      </el-table-column>

      <!-- 状态 -->
      <el-table-column label="状态" width="80">
        <template #default="{ row }">
          <el-tag 
            :type="row.status === 'active' ? 'success' : 'info'"
            size="small"
          >
            {{ row.status === 'active' ? '启用' : '禁用' }}
          </el-tag>
        </template>
      </el-table-column>

      <!-- 加入时间 -->
      <el-table-column label="加入时间" width="110">
        <template #default="{ row }">
          {{ formatDate(row.joinedAt || row.createdAt) }}
        </template>
      </el-table-column>

      <!-- 操作 -->
      <el-table-column label="操作" width="200" fixed="right">
        <template #default="{ row }">
          <div class="action-buttons">
            <!-- 查看课表 -->
            <el-button
              type="primary"
              size="small"
              text
              @click="handleViewSchedule(row)"
              v-if="canViewSchedule(row)"
            >
              <el-icon><Calendar /></el-icon>
              课表
            </el-button>

            <!-- 编辑 -->
            <el-button
              type="primary"
              size="small"
              text
              @click="handleEdit(row)"
              v-if="canEditTeacher(row)"
            >
              <el-icon><Edit /></el-icon>
              编辑
            </el-button>

            <!-- 分配教学组 -->
            <el-dropdown 
              v-if="canAssignGroup(row)" 
              @command="(groupId) => handleAssignGroup(row, groupId)"
            >
              <el-button type="success" size="small" text>
                <el-icon><Connection /></el-icon>
                分配组
                <el-icon><ArrowDown /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="">
                    <span style="color: #909399;">移出教学组</span>
                  </el-dropdown-item>
                  <el-dropdown-item 
                    v-for="group in groupOptions"
                    :key="group.value"
                    :command="group.value"
                    :disabled="group.value === row.groupId"
                  >
                    {{ group.label }}
                    <el-tag v-if="group.value === row.groupId" size="small" type="info" style="margin-left: 8px;">
                      当前
                    </el-tag>
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>

            <!-- 删除 -->
            <el-button
              type="danger"
              size="small"
              text
              @click="handleDelete(row)"
              v-if="canDeleteTeacher(row)"
            >
              <el-icon><Delete /></el-icon>
              删除
            </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { Calendar, Edit, Delete, Connection, ArrowDown } from '@element-plus/icons-vue'
import { useTeacherPermissions } from '../composables/useTeacherPermissions'
import { useTeacherGroupStore } from '@/stores/teacherGroup'

const props = defineProps({
  data: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  },
  selected: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits([
  'selection-change',
  'edit',
  'delete',
  'view-schedule',
  'assign-group'
])

// 权限控制
const {
  canViewSchedule,
  canEditTeacher,
  canDeleteTeacher,
  canAssignGroup
} = useTeacherPermissions()

// 教学组选项
const groupStore = useTeacherGroupStore()
const groupOptions = computed(() => groupStore.groupOptions)

// 方法
const handleSelectionChange = (selection) => {
  emit('selection-change', selection)
}

const handleEdit = (row) => {
  emit('edit', row)
}

const handleDelete = (row) => {
  emit('delete', row)
}

const handleViewSchedule = (row) => {
  emit('view-schedule', row)
}

const handleAssignGroup = (row, groupId) => {
  emit('assign-group', row, groupId)
}

const formatDate = (dateString) => {
  if (!dateString) return '-'
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN')
}
</script>

<style scoped>
.teacher-list {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.name-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.name {
  font-weight: 500;
}

.group-cell {
  display: flex;
  align-items: center;
}

.no-group {
  color: #909399;
  font-size: 12px;
}

.action-buttons {
  display: flex;
  align-items: center;
  gap: 4px;
  flex-wrap: wrap;
}

:deep(.el-table__cell) {
  padding: 12px 0;
}

:deep(.el-button + .el-button) {
  margin-left: 4px;
}
</style>