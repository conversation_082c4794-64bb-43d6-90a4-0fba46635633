<template>
  <el-dialog
    v-model="dialogVisible"
    title="批量分配教学组"
    width="500px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="dialog-content">
      <div class="info-section">
        <el-alert
          :title="`已选择 ${teacherIds.length} 位老师`"
          type="info"
          :closable="false"
          show-icon
        />
      </div>

      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="100px"
        style="margin-top: 20px;"
      >
        <el-form-item label="目标教学组" prop="groupId">
          <el-select 
            v-model="formData.groupId" 
            placeholder="请选择要分配的教学组"
            style="width: 100%"
            clearable
          >
            <el-option
              label="移出所有教学组"
              value=""
            >
              <div style="display: flex; align-items: center;">
                <el-icon style="margin-right: 8px; color: #F56C6C;"><Close /></el-icon>
                <span>移出所有教学组</span>
              </div>
            </el-option>
            <el-option
              v-for="group in groupOptions"
              :key="group.value"
              :label="group.label"
              :value="group.value"
              :disabled="group.disabled"
            >
              <div style="display: flex; align-items: center; justify-content: space-between;">
                <span>{{ group.label }}</span>
                <el-tag v-if="group.disabled" size="small" type="info">已禁用</el-tag>
              </div>
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="操作说明">
          <el-text type="info" size="small">
            <div v-if="formData.groupId === ''">
              将选中的老师从当前教学组中移除，成为未分配状态
            </div>
            <div v-else-if="formData.groupId">
              将选中的老师分配到"{{ selectedGroupName }}"教学组
            </div>
            <div v-else>
              请选择目标教学组
            </div>
          </el-text>
        </el-form-item>
      </el-form>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button 
          type="primary" 
          :loading="submitLoading"
          @click="handleSubmit"
        >
          确认分配
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Close } from '@element-plus/icons-vue'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  teacherIds: {
    type: Array,
    default: () => []
  },
  groupOptions: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['update:visible', 'success'])

// 响应式数据
const formRef = ref()
const submitLoading = ref(false)

const formData = reactive({
  groupId: ''
})

// 表单验证规则
const formRules = {
  groupId: [
    { required: true, message: '请选择目标教学组', trigger: 'change' }
  ]
}

// 计算属性
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

const selectedGroupName = computed(() => {
  if (!formData.groupId) return ''
  const group = props.groupOptions.find(g => g.value === formData.groupId)
  return group?.label || ''
})

// 监听对话框打开/关闭
watch(() => props.visible, (visible) => {
  if (visible) {
    resetForm()
  }
})

// 方法
const resetForm = () => {
  formData.groupId = ''
  if (formRef.value) {
    formRef.value.clearValidate()
  }
}

const handleSubmit = async () => {
  try {
    // 如果选择的是空字符串（移出教学组），跳过表单验证
    if (formData.groupId !== '') {
      await formRef.value.validate()
    }
    
    // 确认操作
    const action = formData.groupId === '' ? '移出教学组' : `分配到"${selectedGroupName.value}"`
    const confirmText = `确定要将 ${props.teacherIds.length} 位老师${action}吗？`
    
    await ElMessageBox.confirm(confirmText, '确认操作', {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    submitLoading.value = true
    
    // 这里应该调用API进行批量分配
    // 暂时模拟操作成功
    await new Promise(resolve => setTimeout(resolve, 1500))
    
    const successText = formData.groupId === '' 
      ? `成功将 ${props.teacherIds.length} 位老师移出教学组`
      : `成功将 ${props.teacherIds.length} 位老师分配到"${selectedGroupName.value}"`
    
    ElMessage.success(successText)
    emit('success', {
      teacherIds: props.teacherIds,
      groupId: formData.groupId
    })
    
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量分配失败:', error)
      if (error.message) {
        ElMessage.error(error.message)
      }
    }
  } finally {
    submitLoading.value = false
  }
}

const handleClose = () => {
  dialogVisible.value = false
  resetForm()
}
</script>

<style scoped>
.dialog-content {
  padding: 0 4px;
}

.info-section {
  margin-bottom: 16px;
}

.dialog-footer {
  text-align: right;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-alert__content) {
  display: flex;
  align-items: center;
}

:deep(.el-select-dropdown__item) {
  padding: 8px 12px;
}
</style>