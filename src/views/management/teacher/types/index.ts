// 教学小组和老师管理相关类型定义

// 教学组数据模型
export interface TeachingGroup {
  id: string
  name: string
  subject?: string
  leaderId: string
  leaderName: string
  description?: string
  teacherCount: number
  status: 'active' | 'inactive'
  createdAt: string
  updatedAt: string
}

// 老师数据模型
export interface Teacher {
  id: string
  name: string
  phone: string
  email?: string
  avatar?: string
  subjects?: string[]
  groupId?: string
  groupName?: string
  role: 'admin' | 'group_leader' | 'teacher'
  status: 'active' | 'inactive'
  joinedAt?: string
  createdAt: string
}

// 教学组成员关系
export interface GroupMembership {
  id: string
  groupId: string
  teacherId: string
  role: 'leader' | 'member'
  joinedAt: string
}

// 权限数据模型
export interface TeacherPermission {
  userId: string
  role: 'admin' | 'group_leader' | 'teacher'
  canManageGroups: boolean
  canManageTeachers: boolean
  canViewAllSchedules: boolean
  canScheduleForOthers: boolean
  managedGroupIds: string[]
}

// API请求参数
export interface GetTeachingGroupsParams {
  keyword?: string
  status?: 'active' | 'inactive'
  pageNum?: number
  pageSize?: number
}

export interface CreateTeachingGroupRequest {
  name: string
  subject?: string
  leaderId: string
  description?: string
}

export interface UpdateTeachingGroupRequest {
  name?: string
  subject?: string
  leaderId?: string
  description?: string
}

export interface GetTeachersParams {
  keyword?: string
  groupId?: string
  role?: string
  status?: 'active' | 'inactive'
  pageNum?: number
  pageSize?: number
}

export interface CreateTeacherRequest {
  name: string
  phone: string
  email?: string
  subjects?: string[]
  role: 'teacher' | 'group_leader'
  groupId?: string
}

export interface UpdateTeacherRequest {
  name?: string
  phone?: string
  email?: string
  subjects?: string[]
  role?: 'teacher' | 'group_leader'
  groupId?: string
}

// 批量操作数据
export interface BatchOperation {
  type: 'assign_group' | 'schedule' | 'cancel'
  teacherIds: string[]
  data: any
}

// 选择器选项
export interface SelectOption {
  label: string
  value: string
  disabled?: boolean
}

// 课程数据模型（复用现有定义）
export interface CourseSchedule {
  id: string
  studentId: string
  studentName: string
  teacherId: string
  teacherName: string
  startTime: string
  endTime: string
  duration: number
  status: 'pending' | 'ongoing' | 'completed' | 'cancelled'
  type: string
  subject: string
  classroom?: string
  cancelReason?: string
  cancelType?: 'teacher' | 'student'
}

// 排课请求数据模型（复用现有定义）
export interface ScheduleRequest {
  studentId: string
  teacherId: string
  dateRange: [string, string]
  weeklySchedules: {
    dayOfWeek: number
    startTime: string
    endTime: string
  }[]
  duration: number
  subject: string
  type: string
}

// 学生数据模型（复用现有定义）
export interface Student {
  id: string
  name: string
  phone: string
  avatar?: string
  grade?: string
  status: 'active' | 'inactive'
}

// 复习课数据模型（复用现有定义）
export interface ReviewPlan {
  id: string
  studentId: string
  studentName: string
  reviewType: 'daily' | 'weekly' | 'monthly'
  wordCount: number
  scheduledDate: string
  status: 'pending' | 'ongoing' | 'completed'
  courseId: string
}

// 停课数据
export interface CancelCourseData {
  reason: string
  type: 'teacher' | 'student'
}

// API响应基础接口
export interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
}

// 分页响应
export interface PageResponse<T> {
  total: number
  rows: T[]
  pageNum: number
  pageSize: number
}