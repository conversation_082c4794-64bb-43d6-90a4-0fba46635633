# 教学管理模块

## 概述
教学管理模块提供了完整的老师和教学组管理功能，支持权限控制、课表集成等核心特性。

## 功能特性

### 🧑‍🏫 老师管理
- **老师列表展示**: 支持分页、搜索、筛选
- **老师信息管理**: 创建、编辑、删除老师档案
- **权限控制**: 基于角色的细粒度权限管理
- **教学组分配**: 支持单个和批量分配教学组
- **课表集成**: 直接查看老师的课表安排

### 👥 教学组管理
- **教学组卡片展示**: 直观的卡片式界面
- **组织架构管理**: 创建、编辑教学组信息
- **组长管理**: 指定和更换教学组组长
- **成员管理**: 管理教学组成员关系
- **课表查看**: 查看整个教学组的课表

## 技术架构

### 📁 目录结构
```
src/views/management/
├── teacher/
│   ├── index.vue                    # 老师管理主页面
│   ├── group.vue                    # 教学组管理页面
│   ├── components/
│   │   ├── TeacherList.vue          # 老师列表组件
│   │   ├── TeacherForm.vue          # 老师表单组件
│   │   ├── TeacherScheduleDialog.vue # 老师课表弹窗
│   │   ├── BatchAssignGroupDialog.vue # 批量分配弹窗
│   │   ├── GroupCard.vue            # 教学组卡片
│   │   ├── GroupForm.vue            # 教学组表单
│   │   ├── GroupMembersDialog.vue   # 成员管理弹窗
│   │   ├── ChangeLeaderDialog.vue   # 更换组长弹窗
│   │   └── GroupScheduleDialog.vue  # 教学组课表弹窗
│   └── composables/
│       └── useTeacherPermissions.js # 权限控制逻辑
├── api/
│   ├── types.ts                     # TypeScript 类型定义
│   ├── teachingGroup.ts             # 教学组 API
│   ├── teacher.ts                   # 老师管理 API
│   └── index.ts                     # 统一导出
└── stores/
    ├── teacherGroup.js              # 教学组状态管理
    └── teacherManagement.js         # 老师管理状态管理
```

### 🏗 核心组件

#### 老师管理 (TeacherManagement)
- **列表展示**: 支持头像、角色标签、教学组信息等
- **搜索筛选**: 按姓名、手机号、教学组、角色筛选
- **批量操作**: 支持批量分配教学组
- **权限控制**: 根据用户角色显示不同操作按钮

#### 教学组管理 (GroupManagement)
- **卡片布局**: 美观的卡片式展示教学组信息
- **组长展示**: 突出显示组长信息和头像
- **统计信息**: 显示成员数量、专业科目等
- **快捷操作**: 管理成员、查看课表等快捷按钮

#### 权限控制 (PermissionControl)
- **角色分离**: 管理员、组长、普通老师三级权限
- **细粒度控制**: 精确到每个操作按钮的权限控制
- **动态权限**: 基于当前用户身份动态显示功能

### 🔧 状态管理

#### 教学组Store (TeacherGroupStore)
```javascript
// 核心状态
const groups = ref([])           // 教学组列表
const currentGroup = ref(null)   // 当前选中的教学组
const groupMembers = ref([])     // 教学组成员
const loading = ref(false)       // 加载状态
const pagination = ref({...})    // 分页信息

// 计算属性
const groupOptions              // 教学组选项
const activeGroups             // 活跃的教学组

// 核心方法
fetchGroups()                  // 获取教学组列表
createGroup()                  // 创建教学组
updateGroup()                  // 更新教学组
deleteGroup()                  // 删除教学组
changeGroupLeader()            // 更换组长
```

#### 老师管理Store (TeacherManagementStore)
```javascript
// 核心状态
const teachers = ref([])         // 老师列表
const currentTeacher = ref(null) // 当前选中的老师
const permissions = ref(null)    // 权限信息

// 计算属性
const teacherOptions            // 老师选项
const activeTeachers           // 活跃老师
const teachersByGroup          // 按教学组分组

// 核心方法
fetchTeachers()               // 获取老师列表
createTeacher()               // 创建老师
updateTeacher()               // 更新老师信息
assignTeacherToGroup()        // 分配教学组
batchAssignGroup()            // 批量分配
```

### 🔐 权限系统

#### 权限级别
1. **管理员 (admin)**: 全部权限
2. **组长 (group_leader)**: 管理本组老师和课表
3. **普通老师 (teacher)**: 查看自己的信息和课表

#### 权限检查函数
```javascript
// 基础权限检查
hasPermission(permission)

// 教学组管理权限
canManageGroup(groupId)
canManageTeacher(teacher)

// 课表相关权限
canScheduleForTeacher(teacher)
canCancelCourse(course)
canViewSchedule(teacher)

// 操作权限
canCreateTeacher()
canDeleteTeacher(teacher)
canEditTeacher(teacher)
```

### 🌐 API 集成

#### 教学组 API
```javascript
// RESTful API 设计
GET    /management/teaching-groups        // 获取教学组列表
POST   /management/teaching-groups        // 创建教学组
PUT    /management/teaching-groups/:id    // 更新教学组
DELETE /management/teaching-groups/:id    // 删除教学组

// 特殊操作
PUT    /management/teaching-groups/:id/leader    // 更换组长
POST   /management/teaching-groups/:id/members   // 添加成员
DELETE /management/teaching-groups/:id/members/:teacherId // 移除成员
```

#### 老师管理 API
```javascript
// RESTful API 设计
GET    /management/teachers              // 获取老师列表
POST   /management/teachers              // 创建老师
PUT    /management/teachers/:id          // 更新老师
DELETE /management/teachers/:id          // 删除老师

// 特殊操作
PUT    /management/teachers/:id/group              // 分配教学组
GET    /management/teachers/:id/permissions        // 获取权限
POST   /management/teachers/batch/assign-group     // 批量分配
```

## 路由配置

```javascript
{
  path: '/management',
  component: Layout,
  redirect: '/management/teacher',
  name: 'Management',
  meta: {
    title: '教学管理',
    icon: 'peoples'
  },
  permissions: ['management:view'],
  children: [
    {
      path: 'teacher',
      component: () => import('@/views/management/teacher/index'),
      name: 'TeacherManagement',
      meta: {
        title: '老师管理',
        icon: 'user'
      },
      permissions: ['management:teacher:list']
    },
    {
      path: 'group',
      component: () => import('@/views/management/teacher/group'),
      name: 'GroupManagement',
      meta: {
        title: '教学组管理',
        icon: 'peoples'
      },
      permissions: ['management:group:list']
    }
  ]
}
```

## 使用指南

### 管理员操作流程
1. **创建教学组**: 进入教学组管理页面，点击"创建教学组"
2. **添加老师**: 进入老师管理页面，点击"创建老师"
3. **分配教学组**: 选择老师，使用批量分配功能或单独分配
4. **指定组长**: 在教学组卡片中更换组长
5. **管理课表**: 通过课表管理功能为老师排课

### 组长操作流程
1. **查看组员**: 在教学组页面查看和管理组员
2. **排课管理**: 为组内老师安排课程
3. **课表监控**: 查看组内老师的课表执行情况
4. **成员管理**: 管理组内老师的基本信息

### 普通老师操作
1. **查看信息**: 查看自己的基本信息和教学组
2. **课表查看**: 查看自己的课表安排
3. **资料更新**: 更新个人资料和专业科目

## 后续开发计划

### 短期计划 (1-2周)
- [ ] 完善成员管理弹窗功能
- [ ] 实现更换组长功能
- [ ] 添加教学组课表聚合显示
- [ ] 完善权限验证逻辑

### 中期计划 (1个月)
- [ ] 添加教学组统计报表
- [ ] 实现老师评价系统
- [ ] 添加课程负载分析
- [ ] 支持教学组层级管理

### 长期计划 (3个月)
- [ ] 智能排课推荐
- [ ] 教学效果分析
- [ ] 移动端适配
- [ ] 消息通知系统

## 注意事项

1. **权限安全**: 所有操作都需要进行权限验证
2. **数据一致性**: 教学组和老师的关联关系需要保持一致
3. **性能优化**: 大量数据时注意分页和懒加载
4. **用户体验**: 提供友好的错误提示和加载状态
5. **数据备份**: 重要操作需要确认和备份机制

## 技术债务

1. 一些弹窗组件目前是占位组件，需要完善功能实现
2. API 调用目前使用模拟数据，需要对接真实后端
3. 权限系统需要与后端权限体系完全对接
4. 需要添加更完善的错误处理和重试机制
5. 组件的单元测试需要补充完善