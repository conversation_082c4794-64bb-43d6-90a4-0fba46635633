import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { teacherManagementApi } from '@/api/management'
import { useCurriculumStore } from './curriculum'

export const useTeacherManagementStore = defineStore('teacherManagement', () => {
  // 状态
  const teachers = ref([])
  const currentTeacher = ref(null)
  const permissions = ref(null)
  const loading = ref(false)
  const pagination = ref({
    pageNum: 1,
    pageSize: 10,
    total: 0
  })

  // 复用课表store
  const curriculumStore = useCurriculumStore()

  // 计算属性
  const teacherOptions = computed(() => 
    teachers.value.map(teacher => ({
      label: teacher.name,
      value: teacher.id,
      disabled: teacher.status === 'inactive'
    }))
  )

  const activeTeachers = computed(() =>
    teachers.value.filter(teacher => teacher.status === 'active')
  )

  const teachersByGroup = computed(() => {
    const groupMap = new Map()
    teachers.value.forEach(teacher => {
      const groupId = teacher.groupId || 'unassigned'
      if (!groupMap.has(groupId)) {
        groupMap.set(groupId, [])
      }
      groupMap.get(groupId).push(teacher)
    })
    return groupMap
  })

  // 方法
  const fetchTeachers = async (params = {}) => {
    loading.value = true
    try {
      const response = await teacherManagementApi.getTeachers({
        ...params,
        pageNum: pagination.value.pageNum,
        pageSize: pagination.value.pageSize
      })
      if (response.code === 200) {
        teachers.value = response.data.rows
        pagination.value.total = response.data.total
        pagination.value.pageNum = response.data.pageNum
        pagination.value.pageSize = response.data.pageSize
      } else {
        ElMessage.error(response.message || '获取老师列表失败')
      }
    } catch (error) {
      console.error('获取老师列表失败:', error)
      ElMessage.error('获取老师列表失败')
    } finally {
      loading.value = false
    }
  }

  const createTeacher = async (data) => {
    try {
      const response = await teacherManagementApi.createTeacher(data)
      if (response.code === 200) {
        ElMessage.success('创建老师成功')
        await fetchTeachers()
        return true
      } else {
        ElMessage.error(response.message || '创建老师失败')
        return false
      }
    } catch (error) {
      console.error('创建老师失败:', error)
      ElMessage.error('创建老师失败')
      return false
    }
  }

  const updateTeacher = async (id, data) => {
    try {
      const response = await teacherManagementApi.updateTeacher(id, data)
      if (response.code === 200) {
        ElMessage.success('更新老师信息成功')
        await fetchTeachers()
        return true
      } else {
        ElMessage.error(response.message || '更新老师信息失败')
        return false
      }
    } catch (error) {
      console.error('更新老师信息失败:', error)
      ElMessage.error('更新老师信息失败')
      return false
    }
  }

  const deleteTeacher = async (id) => {
    try {
      const response = await teacherManagementApi.deleteTeacher(id)
      if (response.code === 200) {
        ElMessage.success('删除老师成功')
        await fetchTeachers()
        return true
      } else {
        ElMessage.error(response.message || '删除老师失败')
        return false
      }
    } catch (error) {
      console.error('删除老师失败:', error)
      ElMessage.error('删除老师失败')
      return false
    }
  }

  const assignTeacherToGroup = async (teacherId, groupId) => {
    try {
      const response = await teacherManagementApi.assignTeacherToGroup(teacherId, groupId)
      if (response.code === 200) {
        ElMessage.success('分配教学组成功')
        await fetchTeachers()
        return true
      } else {
        ElMessage.error(response.message || '分配教学组失败')
        return false
      }
    } catch (error) {
      console.error('分配教学组失败:', error)
      ElMessage.error('分配教学组失败')
      return false
    }
  }

  const batchAssignGroup = async (teacherIds, groupId) => {
    try {
      const response = await teacherManagementApi.batchAssignGroup(teacherIds, groupId)
      if (response.code === 200) {
        ElMessage.success('批量分配教学组成功')
        await fetchTeachers()
        return true
      } else {
        ElMessage.error(response.message || '批量分配教学组失败')
        return false
      }
    } catch (error) {
      console.error('批量分配教学组失败:', error)
      ElMessage.error('批量分配教学组失败')
      return false
    }
  }

  const fetchTeacherPermissions = async (teacherId) => {
    try {
      const response = await teacherManagementApi.getTeacherPermissions(teacherId)
      if (response.code === 200) {
        permissions.value = response.data
      } else {
        ElMessage.error(response.message || '获取老师权限失败')
      }
    } catch (error) {
      console.error('获取老师权限失败:', error)
      ElMessage.error('获取老师权限失败')
    }
  }

  // 集成课表相关功能
  const getTeacherSchedule = async (teacherId, params) => {
    return await curriculumStore.fetchSchedules({
      ...params,
      teacherId
    })
  }

  const scheduleForTeacher = async (teacherId, scheduleData) => {
    return await curriculumStore.createSchedule({
      ...scheduleData,
      teacherId
    })
  }

  const cancelTeacherCourse = async (courseId, cancelData) => {
    return await curriculumStore.cancelCourse(courseId, cancelData)
  }

  // 批量操作
  const batchSchedule = async (teacherIds, scheduleData) => {
    try {
      const promises = teacherIds.map(teacherId => 
        scheduleForTeacher(teacherId, scheduleData)
      )
      const results = await Promise.allSettled(promises)
      
      const successCount = results.filter(result => result.status === 'fulfilled').length
      const failCount = results.filter(result => result.status === 'rejected').length
      
      if (successCount > 0) {
        ElMessage.success(`批量排课完成：成功${successCount}个，失败${failCount}个`)
      } else {
        ElMessage.error('批量排课失败')
      }
      
      return successCount > 0
    } catch (error) {
      console.error('批量排课失败:', error)
      ElMessage.error('批量排课失败')
      return false
    }
  }

  const setCurrentTeacher = (teacher) => {
    currentTeacher.value = teacher
  }

  const setPagination = (page) => {
    pagination.value.pageNum = page
  }

  const resetPagination = () => {
    pagination.value.pageNum = 1
  }

  const clearPermissions = () => {
    permissions.value = null
  }

  return {
    // 状态
    teachers,
    currentTeacher,
    permissions,
    loading,
    pagination,
    
    // 计算属性
    teacherOptions,
    activeTeachers,
    teachersByGroup,
    
    // 方法
    fetchTeachers,
    createTeacher,
    updateTeacher,
    deleteTeacher,
    assignTeacherToGroup,
    batchAssignGroup,
    fetchTeacherPermissions,
    getTeacherSchedule,
    scheduleForTeacher,
    cancelTeacherCourse,
    batchSchedule,
    setCurrentTeacher,
    setPagination,
    resetPagination,
    clearPermissions
  }
})