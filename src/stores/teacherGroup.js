import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { teachingGroupApi } from '@/api/management'

export const useTeacherGroupStore = defineStore('teacherGroup', () => {
  // 状态
  const groups = ref([])
  const currentGroup = ref(null)
  const groupMembers = ref([])
  const loading = ref(false)
  const pagination = ref({
    pageNum: 1,
    pageSize: 10,
    total: 0
  })

  // 计算属性
  const groupOptions = computed(() => 
    groups.value.map(group => ({
      label: group.name,
      value: group.id,
      disabled: group.status === 'inactive'
    }))
  )

  const activeGroups = computed(() =>
    groups.value.filter(group => group.status === 'active')
  )

  // 方法
  const fetchGroups = async (params = {}) => {
    console.log('🔍 [DEBUG] teacherGroupStore.fetchGroups 开始')
    console.log('🔍 [DEBUG] 请求参数:', params)
    console.log('🔍 [DEBUG] teachingGroupApi:', teachingGroupApi)
    
    loading.value = true
    try {
      const response = await teachingGroupApi.getGroups({
        ...params,
        pageNum: pagination.value.pageNum,
        pageSize: pagination.value.pageSize
      })
      
      console.log('🔍 [DEBUG] API 响应:', response)
      
      if (response.code === 200) {
        groups.value = response.data.rows
        pagination.value.total = response.data.total
        pagination.value.pageNum = response.data.pageNum
        pagination.value.pageSize = response.data.pageSize
        console.log('✅ [DEBUG] 教学组数据更新成功:', groups.value)
      } else {
        console.error('❌ [DEBUG] API 返回错误:', response)
        ElMessage.error(response.message || '获取教学组列表失败')
      }
    } catch (error) {
      console.error('❌ [DEBUG] 获取教学组列表失败:', error)
      ElMessage.error('获取教学组列表失败')
    } finally {
      loading.value = false
    }
  }

  const createGroup = async (data) => {
    try {
      const response = await teachingGroupApi.createGroup(data)
      if (response.code === 200) {
        ElMessage.success('创建教学组成功')
        await fetchGroups()
        return true
      } else {
        ElMessage.error(response.message || '创建教学组失败')
        return false
      }
    } catch (error) {
      console.error('创建教学组失败:', error)
      ElMessage.error('创建教学组失败')
      return false
    }
  }

  const updateGroup = async (id, data) => {
    try {
      const response = await teachingGroupApi.updateGroup(id, data)
      if (response.code === 200) {
        ElMessage.success('更新教学组成功')
        await fetchGroups()
        return true
      } else {
        ElMessage.error(response.message || '更新教学组失败')
        return false
      }
    } catch (error) {
      console.error('更新教学组失败:', error)
      ElMessage.error('更新教学组失败')
      return false
    }
  }

  const deleteGroup = async (id) => {
    try {
      const response = await teachingGroupApi.deleteGroup(id)
      if (response.code === 200) {
        ElMessage.success('删除教学组成功')
        await fetchGroups()
        return true
      } else {
        ElMessage.error(response.message || '删除教学组失败')
        return false
      }
    } catch (error) {
      console.error('删除教学组失败:', error)
      ElMessage.error('删除教学组失败')
      return false
    }
  }

  const changeGroupLeader = async (groupId, newLeaderId) => {
    try {
      const response = await teachingGroupApi.changeGroupLeader(groupId, newLeaderId)
      if (response.code === 200) {
        ElMessage.success('更换组长成功')
        await fetchGroups()
        await fetchGroupMembers(groupId)
        return true
      } else {
        ElMessage.error(response.message || '更换组长失败')
        return false
      }
    } catch (error) {
      console.error('更换组长失败:', error)
      ElMessage.error('更换组长失败')
      return false
    }
  }

  const addTeacherToGroup = async (groupId, teacherId) => {
    try {
      const response = await teachingGroupApi.addTeacherToGroup(groupId, teacherId)
      if (response.code === 200) {
        ElMessage.success('添加老师成功')
        await fetchGroups()
        await fetchGroupMembers(groupId)
        return true
      } else {
        ElMessage.error(response.message || '添加老师失败')
        return false
      }
    } catch (error) {
      console.error('添加老师失败:', error)
      ElMessage.error('添加老师失败')
      return false
    }
  }

  const removeTeacherFromGroup = async (groupId, teacherId) => {
    try {
      const response = await teachingGroupApi.removeTeacherFromGroup(groupId, teacherId)
      if (response.code === 200) {
        ElMessage.success('移除老师成功')
        await fetchGroups()
        await fetchGroupMembers(groupId)
        return true
      } else {
        ElMessage.error(response.message || '移除老师失败')
        return false
      }
    } catch (error) {
      console.error('移除老师失败:', error)
      ElMessage.error('移除老师失败')
      return false
    }
  }

  const fetchGroupMembers = async (groupId) => {
    try {
      const response = await teachingGroupApi.getGroupMembers(groupId)
      if (response.code === 200) {
        groupMembers.value = response.data
      } else {
        ElMessage.error(response.message || '获取组成员失败')
      }
    } catch (error) {
      console.error('获取组成员失败:', error)
      ElMessage.error('获取组成员失败')
    }
  }

  const setCurrentGroup = (group) => {
    currentGroup.value = group
  }

  const setPagination = (page) => {
    pagination.value.pageNum = page
  }

  const resetPagination = () => {
    pagination.value.pageNum = 1
  }

  return {
    // 状态
    groups,
    currentGroup,
    groupMembers,
    loading,
    pagination,
    
    // 计算属性
    groupOptions,
    activeGroups,
    
    // 方法
    fetchGroups,
    createGroup,
    updateGroup,
    deleteGroup,
    changeGroupLeader,
    addTeacherToGroup,
    removeTeacherFromGroup,
    fetchGroupMembers,
    setCurrentGroup,
    setPagination,
    resetPagination
  }
})