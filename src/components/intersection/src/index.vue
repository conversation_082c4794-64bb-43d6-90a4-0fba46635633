<template>
  <div ref="scrollendPlaceholderRef" class="scrollend-placeholder"></div>
</template>

<script setup lang="ts">
  import { ref, onMounted, onBeforeUnmount } from 'vue';
  const emit = defineEmits<{
    (e: 'next'): void;
  }>();
  const scrollendPlaceholderRef = ref<Element>();
  let intersectionObserver: IntersectionObserver;
  onMounted(() => {
    intersectionObserver = new IntersectionObserver(([{ isIntersecting }]) => {
      if (!isIntersecting) {
        return;
      }
      emit('next');
    }, {
      threshold: 0,
      rootMargin: '3.3333%',
    });

    intersectionObserver.observe(scrollendPlaceholderRef.value as Element);
  });

  onBeforeUnmount(() => {
    intersectionObserver.unobserve(scrollendPlaceholderRef.value as Element);
    intersectionObserver.disconnect();
  });
</script>

<script lang="ts">
  export default {
    name: 'JeefPageIntersection',
  };
</script>

<style scoped>
  .scrollend-placeholder {
    width: 100%;
    height: 1px;
  }
</style>
