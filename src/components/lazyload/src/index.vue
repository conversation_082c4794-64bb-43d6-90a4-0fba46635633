<template>
  <!-- <section class="scroll-container"> -->
    <slot></slot>
    <!-- 无数据 -->
    <!-- <i v-if="status === 'empty'" class="empty">{{ noDataText || '暂无数据' }}</i> -->
    <!-- 数据全部加载完成 -->
    <!-- <i v-if="status === 'finished'" class="loaded">{{ finishText || '我是有底线的' }}</i> -->
    <!-- 数据加载中 -->
    <!-- <i v-if="status === 'loading'" class="loading">数据加载中...</i> -->
    <!-- 数据加载错误 -->
    <!-- <i v-if="status === 'error'" class="loading">数据加载异常</i> -->
    <JeefPageIntersection @next="next"></JeefPageIntersection>
  <!-- </section> -->
</template>

<script setup lang="ts">
  import { JeefPageIntersection } from '../../intersection';

  const props = withDefaults(defineProps<{
    status: 'loading' | 'finished' | 'null' | 'empty' | 'error';
    noDataText?: string;
    finishText?: string;
  }>(), {
    status: 'null',
  });

  const emit = defineEmits<{
    (e: 'next'): void;
  }>();

  const next = () => {
    // ElMessage.success('触底了');
    if (props.status === 'null') {
      // ElMessage.success('加载下一页');
      emit('next');
    }
  };
</script>

<script lang="ts">
  export default {
    name: 'JeefLazyload',
  };
</script>

<style scoped>
  .scroll-container {
    position: relative;
    width: 100%;
  }

  .scroll-container > i {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    font-size: 14px;
    font-style: normal;
    color: var(--gray-400);
    text-align: center;
    user-select: none;
  }

  .scroll-container > .empty {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }


  .loaded::before,
  .loaded::after {
    width: 15%;
    height: 0;
    content: "";
    border-bottom: 1px solid var(--gray-200);
  }
</style>
